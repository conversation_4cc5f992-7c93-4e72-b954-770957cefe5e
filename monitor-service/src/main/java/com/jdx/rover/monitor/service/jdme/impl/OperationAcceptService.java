package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.dto.jdme.JdmeGroup;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.service.jdme.IAccidentFlowEventService;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 区域安全员跟进事故处理
 */
@Slf4j
public class OperationAcceptService extends AbstractJdmeMessageService implements IAccidentFlowEventService {
    private AccidentJdmePushManager accidentJdmePushManager;
    private AccidentManager accidentManager;

    public OperationAcceptService() {
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        accidentManager = SpringUtil.getBean(AccidentManager.class);
    }

    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operator 操作员
     * @param repeated 重试消息
     */
    @Override
    public void handleMessage(String accidentNo, String accidentFlowType, String operateType, String operator, boolean repeated) {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if(null == accident) {
            log.warn("区域安全员跟进事故时未找到事故编号[{}]对应的事故信息,忽略处理！", accidentNo);
            return;
        }
        AccidentJdmePush accidentJdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        if(null == accidentJdmePush) {
            log.warn("区域安全员跟进事故时未找到事故推送消息，影子事件编号：【{}】", accident.getShadowEventId());
            return;
        }

        //影子事件订阅
        try {
            ShadowSubscribeEventTaskDTO shadowEventTask = subscribeShadowEvent(accident);
            //持久化异常提报的需要发送的卡片消息
            if (null != shadowEventTask) {
                accidentJdmePush.setStatus(shadowEventTask.getStatus());
                accidentJdmePush.setVideoUrl(shadowEventTask.getVideoUrl());
                accidentJdmePush.setEventPlayUrl(shadowEventTask.getEventPlayUrl());
            }
        } catch (Exception e) {
            log.info("订阅影子事件[{}]订阅失败: {}", accident.getShadowEventId(), e);
        }
        accidentJdmePush.setAccidentFlowType(accidentFlowType);
        //区域安全员跟进事故时自动进群
        String groupId = accidentJdmePush.getAccidentGroupId();
        if(StrUtil.isBlank(groupId)) {
            //根据当前事故编码创建新京ME群，从而获取到新群号，作为卡片消息的参数之一发送出去
            JdmeGroup jdmeGroup = buildJdmeGroup(accidentJdmePush, accident);
            groupId = accidentJdmePushManager.createGroup(jdmeGroup);
            accidentJdmePush.setAccidentGroupId(groupId);
        }
        this.accidentJdmePushManager.updateById(accidentJdmePush);
        addMemberIntoGroup(groupId, null, operator);
    }
}
