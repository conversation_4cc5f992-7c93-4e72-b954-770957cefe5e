package com.jdx.rover.monitor.service.cockpit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Sets;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.map.api.domain.dto.MapInfoDTO;
import com.jdx.rover.metadata.domain.dto.cockpit.CockpitBasicInfoDTO;
import com.jdx.rover.metadata.domain.dto.vehicle.VehicleStationInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.api.domain.enums.AcceptIssueStatusEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitModeEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitStatusEnum;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.constant.MapCollectConstant;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleDistanceDO;
import com.jdx.rover.monitor.dto.cockpit.CockpitSingleVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitStationVehicleListDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.HDMapInfoDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitVehiclePageDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.MonitorScheduleStopEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.scene.DriveSceneEnum;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitManager;
import com.jdx.rover.monitor.manager.cockpit.CockpitTeamManager;
import com.jdx.rover.monitor.manager.config.DuccMonitorProperties;
import com.jdx.rover.monitor.manager.map.MapInfoManager;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskTimeRecordManager;
import com.jdx.rover.monitor.manager.schedule.ScheduleStateManager;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.vehicle.IntersectionManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleRequireManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleStatusManager;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTaskTimeRecord;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleMapDistanceRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.metadata.CockpitVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.sort.CockpitScoreSortedSetRepository;
import com.jdx.rover.monitor.vo.cockpit.BindVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitSingleVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehicleSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.ticket.domain.dto.query.CockpitIssuesDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryCockpitVO;
import jdx.rover.guardian.data.dto.GuardianDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 单车页服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CockpitVehicleService {

    private final VehicleAlarmRepository vehicleAlarmRepository;
    private final VehicleScheduleRepository vehicleScheduleRepository;
    private final VehicleRealtimeRepository vehicleRealtimeRepository;
    private final VehicleStatusRepository vehicleStatusRepository;
    private final VehicleBasicRepository vehicleBasicRepository;
    private final CockpitManager cockpitManager;

    private final CockpitTeamManager cockpitTeamManager;
    private final CockpitScoreSortedSetRepository cockpitScoreSortedSetRepository;
    private final IssueQueryApiManager issueQueryApiManager;
    private final CockpitStatusRepository cockpitStatusRepository;
    private final VehicleTakeOverRepository vehicleTakeOverRepository;
    private final ScheduleStateManager scheduleStateManager;
    private final CockpitVehicleNameRepository cockpitVehicleNameRepository;
    private final MapCollectionTaskManager mapCollectionTaskManager;
    private final GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;
    private final DuccMonitorProperties duccMonitorProperties;
    private final VehicleMapDistanceRepository vehicleMapDistanceRepository;
    private final MapCollectionTaskTimeRecordManager mapCollectionTaskTimeRecordManager;
    private final MapInfoManager mapInfoManager;
    private final VehicleRequireManager vehicleRequireManager;

    private static final double kHeadingOffset = Math.PI / 2;
    private static final double kCircleOffset = 2 * Math.PI;
    private static final double kConvertAngle = 180.0 / Math.PI;

    /**
     * 获取大屏多车页数据
     *
     * @param cockpitNumber
     * @param pageNum
     * @param pageSize
     * @return
     */
    public MultiCockpitVehiclePageDTO<CockpitVehicleDTO> getVehicleList(String cockpitNumber, Integer pageNum, Integer pageSize) {
        Set<String> cockpitTeamVehicleSet = getByCondition(cockpitNumber);

        List<String> vehicleNameOrderList = new ArrayList<>(cockpitTeamVehicleSet);
        MultiCockpitVehiclePageDTO<CockpitVehicleDTO> pageDTO = new MultiCockpitVehiclePageDTO<>();
        pageDTO.setPageNum(pageNum);
        pageDTO.setPageSize(pageSize);
        setPagesAndTotal(pageDTO, vehicleNameOrderList);

        int scheduleVehicleCount = scheduleStateManager.getHaveScheduleCount(vehicleNameOrderList);
        pageDTO.setScheduleVehicleCount(scheduleVehicleCount);

        List<String> vehicleNamePageList = getVehicleNamePageList(pageDTO, vehicleNameOrderList);
        List<CockpitVehicleDTO> list = getMultiVehicleList(vehicleNamePageList);
        pageDTO.setList(list);
        return pageDTO;
    }

    /**
     * 创建空的分页结果
     */
    private MultiCockpitVehiclePageDTO<CockpitVehicleDTO> createEmptyPageResult(Integer pageNum, Integer pageSize) {
        MultiCockpitVehiclePageDTO<CockpitVehicleDTO> pageDTO = new MultiCockpitVehiclePageDTO<>();
        pageDTO.setPageNum(pageNum);
        pageDTO.setPageSize(pageSize);
        pageDTO.setTotal(0L);
        pageDTO.setPages(0);
        pageDTO.setScheduleVehicleCount(0);
        pageDTO.setList(new ArrayList<>());
        return pageDTO;
    }

    /**
     * 设置分页的总数,总页数
     */
    private void setPagesAndTotal(PageDTO<CockpitVehicleDTO> pageDTO, List<String> vehicleNameOrderList) {
        Long pageTotal = (long) vehicleNameOrderList.size();
        pageDTO.setTotal(pageTotal);

        double pagesDouble = Math.ceil(pageDTO.getTotal().doubleValue() / pageDTO.getPageSize());
        Integer pages = (int) pagesDouble;
        pageDTO.setPages(pages);
    }

    /**
     * 设置分页数据
     */
    private List<String> getVehicleNamePageList(PageDTO<CockpitVehicleDTO> pageDTO, List<String> vehicleNameOrderList) {
        int startIndex = (pageDTO.getPageNum() - 1) * pageDTO.getPageSize();
        if (startIndex < 0) {
            startIndex = 0;
        }
        int endIndex = startIndex + pageDTO.getPageSize();
        if (endIndex > vehicleNameOrderList.size()) {
            endIndex = vehicleNameOrderList.size();
        }
        List<String> vehicleNamePageList = new ArrayList<>();
        if (endIndex > startIndex) {
            vehicleNamePageList.addAll(vehicleNameOrderList.subList(startIndex, endIndex));
        }
        return vehicleNamePageList;
    }

    /**
     * 设置分页数据
     */
    private List<CockpitVehicleDTO> getMultiVehicleList(List<String> vehicleNameList) {
        List<CockpitVehicleDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return list;
        }
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        Map<String, VehicleAlarmDO> alarmMap = vehicleAlarmRepository.listMap(vehicleNameList);
        Map<String, MonitorScheduleEntity> monitorScheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNameList);
        Map<String, VehicleBasicDTO> vehicleMonitorBasicDtoMap = vehicleBasicRepository.listMap(vehicleNameList);
        Map<String, VehicleStatusDO> vehicleStatusMap = vehicleStatusRepository.listMap(vehicleNameList);
        Map<String, VehicleTakeOverEntity> vehicleTakeOverMap = vehicleTakeOverRepository.listMap(vehicleNameList);
        for (String vehicleName : vehicleNameList) {
            VehicleStatusDO vehicleStatus = vehicleStatusMap.get(vehicleName);
            CockpitVehicleDTO vehicle = new CockpitVehicleDTO();
            vehicle.setVehicleName(vehicleName);
            vehicle.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
            vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
            VehicleBasicDTO vehicleMonitorBasicDto = vehicleMonitorBasicDtoMap.get(vehicleName);
            if (!Objects.isNull(vehicleMonitorBasicDto)) {
                vehicle.setStationName(vehicleMonitorBasicDto.getStationName());
            }
            VehicleRealtimeInfoDTO realtime = realtimeMap.get(vehicleName);
            if (!Objects.isNull(realtime)) {
                String systemState = realtime.getSystemState();
                vehicle.setSystemState(systemState);
                vehicle.setRecordTime(realtime.getRecordTime());
                if (VehicleStatusManager.isGuardianOnline(vehicleStatus)) {
                    vehicle.setVehicleState(realtime.getVehicleState());
                    vehicle.setSpeed(Optional.ofNullable(realtime.getSpeed()).orElse(0.0).floatValue());
                    VehicleAlarmDO alarm = alarmMap.get(vehicleName);
                    if (alarm != null && CollectionUtils.isNotEmpty(alarm.getAlarmEventList())) {
                        List<CockpitVehicleDTO.AlarmEvent> alarmEventList = alarm.getAlarmEventList().stream().map(tmp -> {
                            CockpitVehicleDTO.AlarmEvent alarmEvent = new CockpitVehicleDTO.AlarmEvent();
                            alarmEvent.setAlarmEvent(tmp.getType());
                            alarmEvent.setReportTime(tmp.getReportTime());
                            return alarmEvent;
                        }).collect(Collectors.toList());
                        vehicle.setAlarmEventList(alarmEventList);
                    }
                }
                setSceneAndSchedule(monitorScheduleEntityMap, vehicleStatus, vehicle, realtime);
            }

            setTakeOverInfo(vehicleTakeOverMap, vehicleName, vehicle);
            list.add(vehicle);
        }
        return list;
    }

    /**
     * 设置场景信息
     */
    private void setSceneAndSchedule(Map<String, MonitorScheduleEntity> monitorScheduleEntityMap, VehicleStatusDO vehicleStatus
            , CockpitVehicleDTO vehicle, VehicleRealtimeInfoDTO realtime) {
        String vehicleName = realtime.getVehicleName();
        List<String> sceneList = new ArrayList<>();
        MonitorScheduleEntity monitorScheduleEntity = monitorScheduleEntityMap.get(vehicleName);
        if (!Objects.isNull(monitorScheduleEntity)) {
            vehicle.setScheduleState(monitorScheduleEntity.getScheduleState());
            vehicle.setGlobalMileage(monitorScheduleEntity.getGlobalMileage());
            vehicle.setArrivedMileage(getArrivedMileage(monitorScheduleEntity, realtime));
            if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState()
                    , VehicleScheduleState.RETURN.getVehicleScheduleState())) {
                // 停靠点场景
                if (isStopAtScene(realtime, monitorScheduleEntity)) {
                    sceneList.add(DriveSceneEnum.STOP_AT.name());
                }
            }
        }
        if (IntersectionManager.isInIntersection(vehicleStatus)) {
            sceneList.add(DriveSceneEnum.INTERSECTION.name());
        }
        if (VehicleStatusManager.isGuardianOnline(vehicleStatus)) {
            vehicle.setSceneList(sceneList);
        }
    }

    /**
     * 设置接管信息
     */
    private static void setTakeOverInfo(Map<String, VehicleTakeOverEntity> vehicleTakeOverMap, String vehicleName, CockpitVehicleDTO vehicle) {
        if (MapUtils.isEmpty(vehicleTakeOverMap)) {
            return;
        }
        VehicleTakeOverEntity takeOver = vehicleTakeOverMap.get(vehicleName);
        if (Objects.isNull(takeOver)) {
            return;
        }
        vehicle.setTakeOverUserName(takeOver.getUserName());
        vehicle.setTakeOverSource(takeOver.getCommandSource());
        vehicle.setTakeOverStatus(takeOver.getOperationStatus());
    }

    /**
     * 读取
     * 不能用retainAll,缓存取出来数据不能修改,否则数据不对
     */
    private Set<String> getByCondition(String cockpitNumber) {
        // 第一步 驾驶舱团队下所有车辆set1
        Set<String> result = cockpitVehicleNameRepository.get(cockpitNumber);
        if (CollectionUtils.isEmpty(result)) {
            return new HashSet<>();
        }
        // 暂时不用本地缓存,没有实现 Set<String> result = cockpitTeamVehicleNameRepository.get(cockpitTeamNumber);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        // 第二步 根据调度状态排序,获取分数排序set2
        Set<String> scoredSortedSet = Sets.newLinkedHashSet(cockpitScoreSortedSetRepository.get());
        if (CollectionUtils.isEmpty(scoredSortedSet)) {
            log.info("驾驶舱团队下车辆为空!cockpitNumber={},scoredSortedSet={}", cockpitNumber, scoredSortedSet);
        }

        // 2个set交集,为排序筛选结果=set1交集set2
        result = Sets.intersection(scoredSortedSet, result);
        if (CollectionUtils.isEmpty(scoredSortedSet)) {
            log.info("驾驶舱团队下车辆为空!username={},userVehicleSet={}", cockpitNumber, result);
        }
        return result;
    }

    /**
     * 当前调度完成里程
     *
     * @param monitorScheduleEntity monitorScheduleEntity
     * @param realtime realtime
     * @return Double
     */
    private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
        Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
        if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(), VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(), VehicleScheduleState.RETURN.getVehicleScheduleState())) {
            Double currentStopFinishedMileage;
            if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
                currentStopFinishedMileage = 0.0;
            } else {
                currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
            }
            finishedMileage += currentStopFinishedMileage;
        }
        return finishedMileage;
    }

    /**
     * 是否停靠点场景
     * @return boolean
     */
    private boolean isStopAtScene(VehicleRealtimeInfoDTO realtime, MonitorScheduleEntity schedule) {
        if (CollectionUtils.isEmpty(schedule.getStop())) {
            return false;
        }
        if (Objects.isNull(realtime) || Objects.isNull(realtime.getLat()) || Objects.isNull(realtime.getLon())) {
            return false;
        }
        for (MonitorScheduleStopEntity stop : schedule.getStop()) {
            if (Objects.isNull(stop.getLat()) || Objects.isNull(stop.getLon())) {
                continue;
            }
            double d = getDistance(realtime.getLat(), realtime.getLon(), stop.getLat(), stop.getLon());
            //小数点后5位精度为1.012277412m,5m对应0.00006
            if (d < 0.00006) {
                return true;
            }
        }
        return false;
    }

    /**
     * 求标准差近似距离
     */
    private double getDistance(double x1, double y1, double x2, double y2) {
        return Math.abs(x1 - x2) + Math.abs(y1 - y2);
    }

    /**
     * 查询车辆列表
     */
    public List<CockpitVehicleDTO> searchVehicleList(CockpitVehicleSearchVO cockpitVehicleSearchVO) {
        List<VehicleStationInfoDTO> list = cockpitManager.getVehicleList(cockpitVehicleSearchVO.getCockpitNumber());
        List<CockpitVehicleDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        String searchName = cockpitVehicleSearchVO.getSearchName();
        for (VehicleStationInfoDTO vehicle : list) {
            if (StringUtils.indexOfIgnoreCase(vehicle.getVehicleName(), searchName) == -1 && StringUtils.indexOfIgnoreCase(vehicle.getStationName(), searchName) == -1) {
                continue;
            }
            CockpitVehicleDTO dto = new CockpitVehicleDTO();
            dto.setVehicleName(vehicle.getVehicleName());
            dto.setStationName(vehicle.getStationName());
            result.add(dto);
            // 最多返回50条
            if (result.size() >= 50) {
                break;
            }
        }
        return result;
    }

    /**
     * 座席绑定/解绑车辆
     *
     * @param bindVehicleVO bindVehicleVO
     * @return MonitorErrorEnum
     */
    public HttpResult<Void> bindVehicle(BindVehicleVO bindVehicleVO) {
        //校验是否有重复车辆
        long count = bindVehicleVO.getVehicleNameList().stream().distinct().count();
        if (count != bindVehicleVO.getVehicleNameList().size()) {
            return HttpResult.error(MonitorErrorEnum.ERROR_COCKPIT_VEHICLE_REPEAT_BIND.getCode(), MonitorErrorEnum.ERROR_COCKPIT_VEHICLE_REPEAT_BIND.getMessage());
        }
        String username = UserUtils.getAndCheckLoginUser();
        String cockpitNumber = bindVehicleVO.getCockpitNumber();
        List<String> vehicleNameList = bindVehicleVO.getVehicleNameList();
        // 1、获取座席原有绑定车辆列表
        List<VehicleStationInfoDTO> historyVehicleList = cockpitManager.getVehicleList(cockpitNumber);
        List<String> historyVehicleNameList = historyVehicleList.stream().map(VehicleStationInfoDTO::getVehicleName).toList();
        // 2、判断解绑车辆
        List<String> deleteVehicleNameList = historyVehicleNameList.stream().filter(v -> !vehicleNameList.contains(v)).toList();
        if (CollectionUtils.isNotEmpty(deleteVehicleNameList)) {
            // 获取当前座席工单处理车辆
            QueryCockpitVO queryCockpitVO = new QueryCockpitVO();
            queryCockpitVO.setCockpitNumber(cockpitNumber);
            CockpitIssuesDTO cockpitIssuesDTO = issueQueryApiManager.queryCockpit(queryCockpitVO);
            if (!Objects.isNull(cockpitIssuesDTO) && CollectionUtils.isNotEmpty(cockpitIssuesDTO.getIssueList())) {
                for (CockpitIssuesDTO.CockpitIssue cockpitIssue : cockpitIssuesDTO.getIssueList()) {
                    if (deleteVehicleNameList.contains(cockpitIssue.getVehicleName())) {
                        String message = String.format(MonitorErrorEnum.ERROR_COCKPIT_VEHICLE_BIND.getMessage(), cockpitIssue.getVehicleName());
                        return HttpResult.error(MonitorErrorEnum.ERROR_COCKPIT_VEHICLE_BIND.getCode(), message);
                    }
                }
            }
        }
        // 3、调用metadata
        cockpitManager.cockpitBindVehicle(cockpitNumber, username, vehicleNameList);
        return HttpResult.success();
    }

    /**
     * 获取座席下站点车辆列表
     *
     * @param cockpitNumber 座席编号
     * @return List
     */
    public List<CockpitStationVehicleListDTO> getStationVehicleList(String cockpitNumber) {
        List<VehicleStationInfoDTO> list = cockpitManager.getVehicleList(cockpitNumber);
        List<CockpitStationVehicleListDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        //数据根据站点进行分组
        Map<String, List<VehicleStationInfoDTO>> collect = list.stream().collect(Collectors.groupingBy(VehicleStationInfoDTO::getStationNumber));

        //参数封装
        collect.entrySet().forEach(entry -> {
            CockpitStationVehicleListDTO stationVehicleListDto = new CockpitStationVehicleListDTO();
            VehicleStationInfoDTO vehicleInfo = entry.getValue().get(0);
            stationVehicleListDto.setCityName(vehicleInfo.getAddressInfo().getCityName());
            stationVehicleListDto.setStationName(vehicleInfo.getStationName());
            stationVehicleListDto.setStationNumber(vehicleInfo.getStationNumber());
            stationVehicleListDto.setVehicleList(entry.getValue().stream().map(VehicleStationInfoDTO::getVehicleName).collect(Collectors.toList()));
            result.add(stationVehicleListDto);
        });
        return result;
    }

    /**
     * 获取全部座席实时状态
     *
     * @return List
     */
    public List<CockpitRealStatusDTO> getAllCockpitRealStatus() {
        log.info("获取全量的座席实时状态");
        List<CockpitRealStatusDTO> cockpitRealStatusDTOList = new ArrayList<>();
        List<CockpitBasicInfoDTO> cockpitBasicInfoList = cockpitTeamManager.getAllCockpitBasicInfoList();
        if (CollectionUtils.isEmpty(cockpitBasicInfoList)) {
            return cockpitRealStatusDTOList;
        }
        List<String> cockpitNumerList = cockpitBasicInfoList.stream().map(cockpit -> cockpit.getCockpitNumber()).collect(Collectors.toList());
        List<CockpitStatusDO> cockpitStatusDOList = cockpitStatusRepository.list(cockpitNumerList);
        cockpitStatusDOList.stream().forEach(cockpitStatus -> {
            CockpitRealStatusDTO cockpitRealStatus = new CockpitRealStatusDTO();
            cockpitRealStatus.setCockpitStatus(Optional.ofNullable(cockpitStatus.getCockpitStatus())
                    .orElse(CockpitStatusEnum.OFFLINE.getValue()));
            cockpitRealStatus.setCockpitMode(cockpitStatus.getCockpitMode());
            cockpitRealStatus.setCockpitNumber(cockpitStatus.getCockpitNumber());
            cockpitRealStatus.setRecordTime(cockpitStatus.getRecordTime());
            cockpitRealStatus.setAcceptIssueStatus(Optional.ofNullable(cockpitStatus.getAcceptIssueStatus())
                    .orElse(AcceptIssueStatusEnum.STOP.getValue()));
            cockpitRealStatus.setCockpitUserName(cockpitStatus.getCockpitUserName());
            cockpitRealStatusDTOList.add(cockpitRealStatus);
        });
        return cockpitRealStatusDTOList;
    }

    /**
     * 获取平行驾驶单车页
     */
    public CockpitSingleVehicleDTO getSingleVehicle(CockpitSingleVehicleVO cockpitSingleVehicleVO) {
        String vehicleName = cockpitSingleVehicleVO.getVehicleName();
        VehicleRealtimeInfoDTO realtime = vehicleRealtimeRepository.get(cockpitSingleVehicleVO.getVehicleName());
        List<VehicleAlarmDO.VehicleAlarmEventDO> alarmList = vehicleAlarmRepository.get(vehicleName);
        MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(vehicleName);
        VehicleStatusDO vehicleStatus = vehicleStatusRepository.get(vehicleName);
        VehicleTakeOverEntity takeOver = vehicleTakeOverRepository.get(vehicleName);
        CockpitStatusDO cockpitStatusDO = cockpitStatusRepository.get(cockpitSingleVehicleVO.getCockpitNumber());
        CockpitSingleVehicleDTO vehicle = new CockpitSingleVehicleDTO();
        vehicle.setVehicleName(vehicleName);
        vehicle.setScheduleState(VehicleScheduleState.WAITING.getVehicleScheduleState());
        vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
        vehicle.setIsUnderRequire(vehicleRequireManager.checkVehicleIsUnderRequireDatabase(vehicleName));
        if (!Objects.isNull(realtime)) {
            vehicle.setSystemState(realtime.getSystemState());
            if (realtime.getDrivableDirection() != null) {
                vehicle.setEnableFront(realtime.getDrivableDirection().getEnableFront());
                vehicle.setEnableBack(realtime.getDrivableDirection().getEnableBack());
                vehicle.setEnableLeft(realtime.getDrivableDirection().getEnableLeft());
                vehicle.setEnableRight(realtime.getDrivableDirection().getEnableRight());
            }
            vehicle.setHeading(realtime.getHeading());

            // 计算车辆实际朝向
            double headingTf = kCircleOffset + kHeadingOffset - realtime.getHeading();
            double headingAngle = BigDecimal.valueOf(headingTf % kCircleOffset).doubleValue();
            vehicle.setRealHeading(headingAngle * kConvertAngle);

            if (VehicleStatusManager.isGuardianOnline(vehicleStatus)) {
                vehicle.setVehicleState(realtime.getVehicleState());
                vehicle.setPower(Optional.ofNullable(realtime.getPower()).orElse(0.0).floatValue());
                vehicle.setSpeed(realtime.getSpeed());
                //告警特殊判断，采图模式走异常
                if (Objects.equals(vehicleStatus.getCollectionMode(), CollectionModeEnum.COLLECTION.getCollectionMode()) && (!Objects.isNull(cockpitStatusDO) && Objects.equals(cockpitStatusDO.getCockpitMode(), CockpitModeEnum.MAP_COLLECTION_MODE.getValue()))) {
                    List<GuardianVehicleAbnormal> guardianVehicleAbnormal = guardianVehicleAbnormalManager.listTodayAbnormal(vehicleName);
                    List<String> abnoramlEventNameList = getAbnormalEventNameList(guardianVehicleAbnormal, vehicleName);
                    vehicle.setAlarmEventNameList(abnoramlEventNameList);
                } else if (CollectionUtils.isNotEmpty(alarmList)) {
                    List<String> alarmEventNameList = alarmList.stream().map(VehicleAlarmDO.VehicleAlarmEventDO::getType).toList();
                    vehicle.setAlarmEventNameList(alarmEventNameList);
                }
            }
            if (!Objects.isNull(realtime.getLon()) && !Objects.isNull(realtime.getLat())) {
                CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(realtime.getLon(), realtime.getLat());
                if (!CoordinateUtil.outOfChina(coordinate.getLng(), coordinate.getLat())) {
                    vehicle.setLongitude(coordinate.getLng());
                    vehicle.setLatitude(coordinate.getLat());
                }
            }
        }
        if (!Objects.isNull(monitorScheduleEntity)) {
            vehicle.setScheduleState(monitorScheduleEntity.getScheduleState());
            vehicle.setGlobalMileage(monitorScheduleEntity.getGlobalMileage());
            vehicle.setArrivedMileage(getArrivedMileage(monitorScheduleEntity, realtime));
        }
        if (!Objects.isNull(takeOver)) {
            vehicle.setTakeOverCockpitNumber(takeOver.getCockpitNumber());
            vehicle.setTakeOverUserName(takeOver.getUserName());
            vehicle.setTakeOverSource(takeOver.getCommandSource());
            vehicle.setTakeOverStatus(takeOver.getOperationStatus());
        }
        if (!Objects.isNull(vehicleStatus)) {
            vehicle.setRunMapState(Optional.ofNullable(vehicleStatus.getRunMapState()).orElse(GuardianDataDto.RoverStatus.RunMapState.RUN_HAVE_MAP.name()));
            if (!Objects.isNull(vehicleStatus.getGpsSignal())) {
                Integer gpsSignal = Integer.valueOf(vehicleStatus.getGpsSignal());
                vehicle.setGpsSignal(gpsSignal);
            }
            if (!Objects.isNull(vehicleStatus.getSceneSignal())) {
                Double sceneSignal = Double.valueOf(vehicleStatus.getSceneSignal());
                vehicle.setSceneSignal(sceneSignal);
            }
        }

        // 获取座席状态
        if (!Objects.isNull(cockpitStatusDO)) {
            vehicle.setCockpitStatus(cockpitStatusDO.getCockpitStatus());
        }

        //设置采图模式字段
        setCollectionInfo(vehicle, vehicleStatus, cockpitStatusDO);
        return vehicle;
    }

    /**
     * 异常转换为告警事件名称
     *
     * @param guardianVehicleAbnormals 异常列表
     * @param vehicleName vehicleName
     * @return 告警事件名称列表
     */
    private List<String> getAbnormalEventNameList(List<GuardianVehicleAbnormal> guardianVehicleAbnormals, String vehicleName) {
        Map<String, String> mapCollectionErrorMap = duccMonitorProperties.getMapCollectionErrorMap();
        Set<String> alarmEventNameSet = new HashSet<>();
        for (GuardianVehicleAbnormal guardianVehicleAbnormal : guardianVehicleAbnormals) {
            if (mapCollectionErrorMap.containsKey(guardianVehicleAbnormal.getErrorCode())) {
                alarmEventNameSet.add(mapCollectionErrorMap.get(guardianVehicleAbnormal.getErrorCode()));
            }
        }

        // 23:00前，计算车辆今日地图采集用时，判断是否生成告警
        if (LocalTime.now().isBefore(LocalTime.of(MapCollectConstant.STORAGE_ALARM_DEADLINE_HOUR, MapCollectConstant.STORAGE_ALARM_DEADLINE_MIN))) {
            long vehicleWorkingTime = mapCollectionTaskTimeRecordManager.calculateVehicleWorkingTime(vehicleName);
            if (vehicleWorkingTime >= MapCollectConstant.MAX_VEHICLE_MAP_COLLECT_TIME) {
                alarmEventNameSet.add(MapCollectConstant.VEHICLE_MAP_COLLECT_STORAGE_ALARM);
            }
        }
        return new ArrayList<>(alarmEventNameSet);
    }

    /**
     * 设置采图模式相关信息
     *
     * @param vehicle vehicle
     * @param vehicleStatus vehicleStatus
     * @param cockpitStatusDO cockpitStatusDO
     */
    private void setCollectionInfo(CockpitSingleVehicleDTO vehicle, VehicleStatusDO vehicleStatus, CockpitStatusDO cockpitStatusDO) {
        if (Objects.isNull(cockpitStatusDO) || !Objects.equals(cockpitStatusDO.getCockpitMode(), CockpitModeEnum.MAP_COLLECTION_MODE.getValue())) {
            return;
        }
        //设置里程信息
        VehicleDistanceDO vehicleDistanceDO = vehicleMapDistanceRepository.get(vehicle.getVehicleName());
        vehicle.setGlobalMileage((Optional.ofNullable(vehicleStatus).map(VehicleStatusDO::getTaskTotalMileage).orElse(0.0)) * 1000);
        vehicle.setArrivedMileage(Optional.ofNullable(vehicleDistanceDO).map(VehicleDistanceDO::getTotalDistance).orElse(0.0));
        if (!Objects.isNull(vehicleStatus)) {
            Integer taskId = vehicleStatus.getTaskId();
            if (taskId != null) {
                MapCollectionTask mapCollectionTask = mapCollectionTaskManager.lambdaQuery().eq(MapCollectionTask::getId, taskId).one();
                vehicle.setTaskId(mapCollectionTask.getId());
                vehicle.setTaskName(mapCollectionTask.getTaskName());
                vehicle.setTaskStatus(mapCollectionTask.getTaskStatus());

                List<MapCollectionTaskTimeRecord> mapCollectionTaskTimeRecords = mapCollectionTaskTimeRecordManager.queryRecordByTaskId(taskId);
                if (CollUtil.isNotEmpty(mapCollectionTaskTimeRecords)) {
                    // 子任务数量
                    vehicle.setSubTaskQuantity(mapCollectionTaskTimeRecords.size());

                    // 今日采集用时
                    int workingTime = 0;
                    for (MapCollectionTaskTimeRecord mapCollectionTaskTimeRecord : mapCollectionTaskTimeRecords) {
                        if (mapCollectionTaskTimeRecord.getStartTime() != null && mapCollectionTaskTimeRecord.getEndTime() != null) {
                            workingTime += (int) DateUtil.between(mapCollectionTaskTimeRecord.getStartTime(), mapCollectionTaskTimeRecord.getEndTime(), DateUnit.MINUTE);
                        } else if (mapCollectionTaskTimeRecord.getEndTime() == null) {
                            workingTime += (int) DateUtil.between(mapCollectionTaskTimeRecord.getStartTime(), DateUtil.date(), DateUnit.MINUTE);
                        }
                    }
                    vehicle.setWorkingTime(workingTime);
                }
            }
            vehicle.setCollectionMode(vehicleStatus.getCollectionMode());
            vehicle.setCollectionStatus(vehicleStatus.getCollectionStatus());
        }
        //设置任务绑定状态
        vehicle.setBindingState(vehicleStatus != null && vehicleStatus.getTaskId() != null);
        vehicle.setIsSwitchStatus(RedissonUtils.hasKey(String.format(RedisKeyEnum.COLLECTION_VEHICLE_STATUS.getValue(), vehicle.getVehicleName())));
        //车辆剩余空间
//        VehicleStorageSpaceInfoDO vehicleStorage = vehicleStorageManager.getVehicleStorage(vehicle.getVehicleName());
//        Optional.ofNullable(vehicleStorage)
//                .map(VehicleStorageSpaceInfoDO::getFree)
//                .ifPresent(vehicle::setFreeSize);
    }

    /**
     * 根据经纬度获取高精地图信息
     *
     * @param positionVO positionVO
     * @return HDMapInfoDTO
     */
    public HDMapInfoDTO getMapInfoByPosition(PositionVO positionVO) {
        HDMapInfoDTO hdMapInfoDTO = new HDMapInfoDTO();
        MapInfoDTO mapInfoDTO = mapInfoManager.getMapByPosition(positionVO.getLatitude(), positionVO.getLongitude());
        if (ObjectUtil.isNotNull(mapInfoDTO)) {
            hdMapInfoDTO.setMapId(mapInfoDTO.getId());
            hdMapInfoDTO.setMapVersion(mapInfoDTO.getMapVersion());
        }
        return hdMapInfoDTO;
    }
}