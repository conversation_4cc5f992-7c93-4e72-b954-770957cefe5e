package com.jdx.rover.monitor.service.callback;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.callback.JdCardActionDataDTO;
import com.jdx.rover.monitor.dto.callback.JdCardCallbackDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.callback.JdCardCallbackVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@RequiredArgsConstructor
@Service
@Slf4j
public class JdCardService {

    private final JmqProducerService jmqProducerService;

    public JdCardCallbackDTO callback(JdCardCallbackVO jdCardCallbackVO) {
        log.info("接收到京ME卡片消息,message:{}", JsonUtils.writeValueAsString(jdCardCallbackVO));
        //目前只处理卡片回调消息
        if (Objects.equals(jdCardCallbackVO.getReqType(), 2)) {
            //判断body是否为空
            if (Objects.isNull(jdCardCallbackVO.getBody())) {
                log.error("京ME卡片消息body为空");
                return new JdCardCallbackDTO();
            }
            //判断actionData是否为空
            JdCardCallbackVO.ActionDataVO actionData = jdCardCallbackVO.getBody().getActionData();
            if (Objects.isNull(actionData)) {
                log.error("京ME卡片消息actionData为空");
                return new JdCardCallbackDTO();
            }
            //判断number是否为空
            String number = actionData.getNumber();
            if (Objects.isNull(number)) {
                log.error("京ME卡片消息number为空");
                return new JdCardCallbackDTO();
            }
            //发送JMQ消息
            JdCardActionDataDTO jdCardActionDataDTO = new JdCardActionDataDTO();
            jdCardActionDataDTO.setBusinessType(actionData.getBusinessType());
            jdCardActionDataDTO.setNumber(actionData.getNumber());
            jdCardActionDataDTO.setOperatorType(actionData.getOperatorType());
            jdCardActionDataDTO.setEnv(actionData.getEnv());
            if (jdCardCallbackVO.getFrom() != null) {
                jdCardActionDataDTO.setUserErp(jdCardCallbackVO.getFrom().getPin());
            }
            jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_JDME_CALLBACK.getTopic(), number, jdCardActionDataDTO);
        }
        return new JdCardCallbackDTO();
    }
}
