/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.service.drive;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.repository.redis.sequence.SequenceNumberDuplicateRepository;
import com.jdx.rover.server.api.domain.dto.drive.DriveConnectDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 接收平行驾驶指令回复信息
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DriveServerVehicleReplyService {
    /**
     * 序列号去重Repository
     */
    private final SequenceNumberDuplicateRepository sequenceNumberDuplicateRepository;

    /**
     * 处理单条信息
     */
    public void handleOneMessage(String message) {
        DriveConnectDTO dto = JsonUtils.readValue(message, DriveConnectDTO.class);
        if (Objects.nonNull(dto.getSequenceNumber()) && dto.getSequenceNumber() > 0) {
            if (sequenceNumberDuplicateRepository.isDuplicateVehicleReply(dto.getClientName(), dto.getSequenceNumber())) {
                log.info("忽略重复的车辆回复消息,clientName:{},sequenceNumber:{}", dto.getClientName(), dto.getSequenceNumber());
                return;
            }
        }
    }
}
