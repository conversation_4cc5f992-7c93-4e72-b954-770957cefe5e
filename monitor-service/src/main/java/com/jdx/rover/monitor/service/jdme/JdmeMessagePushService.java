package com.jdx.rover.monitor.service.jdme;

import cn.hutool.core.util.StrUtil;
import com.jdx.rover.infrastructure.api.domain.entity.dto.xingyun.CreateBugDTO;
import com.jdx.rover.metadata.api.domain.enums.VehicleOwnerUseCaseEnum;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.service.jdme.impl.AbstractJdmeMessageService;
import com.jdx.rover.monitor.vo.BugAddVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@RequiredArgsConstructor
@Service
@Slf4j
public class JdmeMessagePushService extends AbstractJdmeMessageService {

    private final AccidentJdmePushManager accidentJdmePushManager;
    private final AccidentManager accidentManager;

    /**
     * 京ME推送消息落库
     * @param bugAddInfo 表单提交数据
     * @param vehicleBasicDto 车辆基本信息
     * @param createBugDTO JIRA响应的BUG信息
     * @param operateUser 当前操作员
     * @return
     */
    public boolean savePushMsg(BugAddVO bugAddInfo, VehicleBasicDTO vehicleBasicDto, CreateBugDTO createBugDTO, String operateUser) {
        //获取事故基本信息,因前面会生成jira中的bugCode，并更新表，因此再次查询获取最新数据
        Accident accident = accidentManager.selectByAccidentNo(bugAddInfo.getIssueNo());
        //持久化异常提报的需要发送的卡片消息
        AccidentJdmePush jdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        buildAccidentJdmePush(jdmePush, bugAddInfo, accident, vehicleBasicDto, createBugDTO, operateUser);
        boolean bool = accidentJdmePushManager.updateById(jdmePush);
        return bool;
    }

    /**
     * 构建异常推送对象
     * @param bugAddInfo
     * @param accident
     * @param vehicleBasicDto
     * @param operateUser
     * @return
     */
    private void buildAccidentJdmePush(AccidentJdmePush jdmePush, BugAddVO bugAddInfo, Accident accident,
                                                   VehicleBasicDTO vehicleBasicDto, CreateBugDTO createBugDTO, String operateUser) {
        //生成标题
        List<Date> jiraDebugTimeList = bugAddInfo.getDebugTime();
        StringBuilder summaryBuilder = new StringBuilder();
        summaryBuilder.append(bugAddInfo.getVehicleName()).append("|")
                .append(VehicleOwnerUseCaseEnum.getNameByValue(vehicleBasicDto.getOwnerUseCase())).append("|").append("【").append(vehicleBasicDto.getStationName()).append("】").append(bugAddInfo.getTopic());
        String debugTimeString = null;
        if (jiraDebugTimeList != null && !jiraDebugTimeList.isEmpty()) {
            SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            StringBuilder jiraDebugTime = new StringBuilder();
            for (Date debugTime : jiraDebugTimeList) {
                jiraDebugTime.append(dateTimeFormat.format(debugTime)).append(";");
            }
            debugTimeString = jiraDebugTime.substring(0, jiraDebugTime.length() - 1);
        }
        //构建持久化对象
        jdmePush.setAccidentNo(bugAddInfo.getIssueNo());
        jdmePush.setTitle(bugAddInfo.getTopic());
        jdmePush.setVehicleName(bugAddInfo.getVehicleName());
//        jdmePush.setLevel(bugAddInfo.getLevel());
        jdmePush.setSeverity(bugAddInfo.getSeverity());
        jdmePush.setBugId(createBugDTO.getBugId());
        jdmePush.setBugCode(createBugDTO.getBugCode());
        jdmePush.setDebugTime(debugTimeString);
        jdmePush.setAccidentAddress(accident.getAccidentAddress());
        jdmePush.setFollower(bugAddInfo.getFollower());
        jdmePush.setCreateUser(operateUser);
        jdmePush.setModifyUser(operateUser);
    }

    /**
     * 根据京ME群号重新发送卡片消息
     * @param groupId
     */
    public void sendJdmeCardMessage(String groupId) throws Exception {
        AccidentJdmePush accidentJdmePush = this.accidentJdmePushManager.getByGroupId(groupId);
        if(null == accidentJdmePush) {
            log.info("根据京ME群号重新发送卡片消息未找到群号[{}]对应的事故推送信息", groupId);
            return;
        }
        String accidentFlowType = accidentJdmePush.getAccidentFlowType();
        if(StrUtil.isBlank(accidentFlowType)) {
            log.info("根据京ME群号重新发送卡片消息时发现处理环节标识[AccidentFlowType]为空!");
            return;
        }
        AccidentFlowEnum flowEnum = AccidentFlowEnum.getByValue(accidentFlowType);
        IAccidentFlowEventService handler = JdmeMessageServiceFactory.create(flowEnum);
       if (handler == null) {
           log.warn("不支持的事故处理流程类型：【{}】", accidentFlowType);
           return;
       }
       String accidentNo = accidentJdmePush.getAccidentNo();
       String operator = accidentJdmePush.getModifyUser();
       handler.handleMessage(accidentNo, accidentFlowType,  null, operator, true);
    }
}
