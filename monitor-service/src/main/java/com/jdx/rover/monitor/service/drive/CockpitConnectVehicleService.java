package com.jdx.rover.monitor.service.drive;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.monitor.api.domain.enums.VehicleOperateSourceEnum;
import com.jdx.rover.monitor.common.utils.proto.ProtoUtils;
import com.jdx.rover.monitor.dto.drive.CommandExecuteResultDTO;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.cockpit.CockpitStatusDO;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.enums.drive.connect.ConnectOperationEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.manager.drive.CockpitConnectVehicleManager;
import com.jdx.rover.monitor.manager.user.UserInfoMqttManager;
import com.jdx.rover.monitor.repository.redis.cockpit.CockpitStatusRepository;
import com.jdx.rover.monitor.repository.redis.sequence.SequenceNumberDuplicateRepository;
import com.jdx.rover.monitor.repository.redis.sequence.SequenceNumberRepository;
import com.jdx.rover.server.api.domain.dto.drive.DriveConnectDTO;
import com.jdx.rover.server.api.domain.enums.mqtt.MqttMessageStateEnum;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import jdx.rover.remote.drive.control.proto.DriveControlDto;
import jdx.rover.remote.drive.proto.DriveHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CockpitConnectVehicleService {
    /**
     * mqtt发送服务
     */
    private final MqttSendJsfService mqttSendJsfService;
    /**
     * 驾舱状态
     */
    private final CockpitStatusRepository cockpitStatusRepository;

    /**
     * 远程驾驶连接服务
     */
    private final CockpitConnectVehicleManager cockpitConnectVehicleManager;

    /**
     * 远程驾驶连接服务
     */
    private final UserInfoMqttManager userInfoMqttManager;

    /**
     * 远程指令服务
     */
    private final DriveRemoteCommandService driveRemoteCommandService;
    /**
     * 序列号生成器
     */
    private final SequenceNumberRepository sequenceNumberRepository;
    /**
     * 序列号去重Repository
     */
    private final SequenceNumberDuplicateRepository sequenceNumberDuplicateRepository;

    /**
     * 处理单条信息
     */
    public void handleOneMessage(String message) {
        DriveConnectDTO dto = JsonUtils.readValue(message, DriveConnectDTO.class);
        if (Objects.nonNull(dto.getSequenceNumber()) && dto.getSequenceNumber() > 0) {
            if (sequenceNumberDuplicateRepository.isDuplicateConnect(dto.getClientName(), dto.getSequenceNumber())) {
                log.info("忽略重复的连接消息,clientName:{},sequenceNumber:{}", dto.getClientName(), dto.getSequenceNumber());
                return;
            }
        }
        if (StringUtils.equals(dto.getMessageState(), MqttMessageStateEnum.FAIL.name())) {
            // 错误消息处理
            handleFail(dto);
        } else if (StringUtils.equals(dto.getClientName(), dto.getCockpitNumber())) {
            // 如果是驾驶舱响应,直接返回前端数据
            handleConnectAllSuccess(dto);
//            sendConnectReplyMqtt(dto);
        } else {
            // 发送驾驶舱mqtt
            if (DriveControlDto.ControlConnect.ConnectOperation.CONNECT == DriveControlDto.ControlConnect.ConnectOperation.valueOf(dto.getConnectOperation())) {
                sendConnectCockpitMqtt(dto.getCockpitNumber(), dto.getVehicleName(), dto.getConnectOperation());
            }
        }

        sendUserOperation(dto);
    }

    /**
     * 发送用户操作信息
     */
    private void sendUserOperation(DriveConnectDTO dto) {
        MonitorUserOperationEntity userOperation = new MonitorUserOperationEntity();
        CockpitStatusDO cockpitStatusDTO = cockpitStatusRepository.get(dto.getCockpitNumber());
        userOperation.setUserName(cockpitStatusDTO.getCockpitUserName());
        userOperation.setVehicleName(dto.getVehicleName());
        userOperation.setTimestamp(new Date());
        String operationType = "REMOTE_DRIVE_" + dto.getConnectOperation() + "_";
        StringBuilder sb = new StringBuilder();
        sb.append(dto.getClientName());
        ConnectOperationEnum connectOperationEnum = ConnectOperationEnum.valueOf(dto.getConnectOperation());
        if (Objects.equals(dto.getClientName(), dto.getVehicleName())) {
            operationType += "VEHICLE";
            sb.append("车辆");
        } else {
            operationType += "COCKPIT";
            sb.append("驾驶舱");
        }
        sb.append("确认").append(connectOperationEnum.getTitle()).append("车辆").append(dto.getVehicleName())
                .append("和").append("驾驶舱").append(dto.getCockpitNumber())
                .append(MqttMessageStateEnum.valueOf(dto.getMessageState()).getTitle()).append("!");

        if (Objects.equals(dto.getMessageState(), MqttMessageStateEnum.FAIL.name())) {
            sb.append("原因为").append(dto.getErrorCode()).append(":").append(dto.getErrorMessage());
        }
        userOperation.setOperationType(operationType);
        userOperation.setOperationMessage(sb.toString());
        userOperation.setOperationSource(VehicleOperateSourceEnum.REMOTE_JOYSTICK.getSource());
        driveRemoteCommandService.sendUserOperation(userOperation);
    }

    /**
     * 处理失败返回消息
     */
    public void handleFail(DriveConnectDTO dto) {
        if (Objects.equals(dto.getConnectOperation(), ConnectOperationEnum.DISCONNECT.name())
                && StringUtils.equalsAny(dto.getErrorCode(), "-10001", "-20001")) {
            // 释放接管时,如果车端连接不存在,直接继续释放驾驶舱接管
            if (Objects.equals(dto.getClientName(), dto.getVehicleName())) {
                sendConnectCockpitMqtt(dto.getCockpitNumber(), dto.getVehicleName(), dto.getConnectOperation());
            } else {
                // 释放接管时,如果驾驶舱连接不存在,直接退出接管成功
                handleConnectAllSuccess(dto);
                dto.setMessageState(MqttMessageStateEnum.SUCCESS.getValue());
            }
        }
//        sendConnectReplyMqtt(dto);
    }

    /**
     * 发送连接mqtt消息
     */
    public void sendConnectCockpitMqtt(String cockpitNumber, String vehicleName, String connectOperation) {
        DriveHeader.RequestHeader.Builder requestHeaderBuilder = DriveHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis());
        requestHeaderBuilder.setRequestId(RequestIdUtils.getRequestId());
        requestHeaderBuilder.setClientName(cockpitNumber);
        requestHeaderBuilder.setNeedResponse(true);
        requestHeaderBuilder.setRetry(false);
        requestHeaderBuilder.setSequenceNumber(sequenceNumberRepository.getDriveConnectNumber(cockpitNumber));

        DriveControlDto.ControlConnect.Builder infoBuilder = DriveControlDto.ControlConnect.newBuilder();
        infoBuilder.setVehicleName(vehicleName);
        infoBuilder.setCockpitName(cockpitNumber);
        infoBuilder.setConnectOperation(DriveControlDto.ControlConnect.ConnectOperation.valueOf(connectOperation));

        infoBuilder.setRequestHeader(requestHeaderBuilder);
        DriveControlDto.ControlConnect info = infoBuilder.build();

        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic(MqttTopicEnum.DRIVE_CONTROL_CONNECT.getTopic() + cockpitNumber);
        mqttMessageVO.setMessage(info.toByteArray());
        mqttSendJsfService.sendBytes(mqttMessageVO);

        mqttMessageVO.setTopic(MqttTopicEnum.CONTROL_CONNECT_SERVER.getTopic() + cockpitNumber);
        mqttSendJsfService.sendBytes(mqttMessageVO);
        log.info("发送请求连接数据topic={},data={}", mqttMessageVO.getTopic(), ProtoUtils.protoToJson(info));
    }

    /**
     * 发送连接响应mqtt消息
     */
    private void sendConnectReplyMqtt(DriveConnectDTO dto) {
        CockpitStatusDO cockpitStatusDTO = cockpitStatusRepository.get(dto.getCockpitNumber());
        if (Objects.isNull(cockpitStatusDTO)) {
            log.error("座舱信息为空!{}", dto);
            return;
        }
        if (Objects.isNull(cockpitStatusDTO.getCockpitUserName())) {
            log.error("车辆接管人信息为空!{},{}", dto, cockpitStatusDTO);
            return;
        }
        CommandExecuteResultDTO result = buildCommandExecuteResultDTO(dto);
        userInfoMqttManager.sendUserInfoMqtt(cockpitStatusDTO.getCockpitUserName(), result);
    }

    /**
     * 构建命令执行结果接口
     */
    private static CommandExecuteResultDTO buildCommandExecuteResultDTO(DriveConnectDTO dto) {
        CommandExecuteResultDTO result = new CommandExecuteResultDTO();
        result.setVehicleName(dto.getVehicleName());
        if (Objects.equals(dto.getMessageState(), MqttMessageStateEnum.FAIL.name())) {
            result.setMessageState(MqttMessageStateEnum.FAIL.name());
        } else {
            result.setMessageState(MqttMessageStateEnum.SUCCESS.name());
        }
        if (Objects.equals(dto.getConnectOperation(), ConnectOperationEnum.CONNECT.name())) {
            result.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.name());
        } else if (Objects.equals(dto.getConnectOperation(), ConnectOperationEnum.DISCONNECT.name())) {
            result.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.name());
        } else {
            result.setRemoteCommandType(dto.getConnectOperation());
        }
        result.setRequestTime(dto.getRecordTime());
        MqttMessageStateEnum mqttMessageStateEnum = MqttMessageStateEnum.valueOf(dto.getMessageState());
        ConnectOperationEnum connectOperationEnum = ConnectOperationEnum.valueOf(dto.getConnectOperation());
        String title = dto.getVehicleName() + connectOperationEnum.getTitle() + mqttMessageStateEnum.getTitle();
        result.setOperationMessage(title);
        result.setFailInfo(dto.getErrorMessage());
        return result;
    }

    /**
     * 处理所有连接成功
     */
    private void handleConnectAllSuccess(DriveConnectDTO dto) {
        CockpitStatusDO cockpitStatusDTO = cockpitStatusRepository.get(dto.getCockpitNumber());
        if (Objects.equals(dto.getConnectOperation(), ConnectOperationEnum.DISCONNECT.name())) {
            // 真正释放,暂时注释
//            cockpitConnectVehicleManager.disconnectSuccess(dto.getVehicleName());
        }
        log.info("用户{}通过驾驶舱{}操作{}车辆{}成功!", cockpitStatusDTO.getCockpitUserName()
                , dto.getCockpitNumber(), dto.getConnectOperation(), dto.getVehicleName());
    }
}
