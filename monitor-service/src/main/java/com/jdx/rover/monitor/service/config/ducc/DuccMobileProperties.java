/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.config.ducc;

import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/16 23:57
 * @description 小程序DUCC配置类
 */
@Component
@Data
public class DuccMobileProperties {

    /**
     * 勘查标记查询半径，单位：米
     */
    @LafValue(key = "mobile.map.mark.radius")
    private Integer markRadius;

    /**
     * 起点终点查询半径，单位：米
     */
    @LafValue(key = "mobile.map.initial.point.radius")
    private Integer initialPointRadius;

    /**
     * 呼叫远驾快捷备注
     */
    @LafValue(key = "mobile.transport.callCockpit.phraseList")
    private List<String> phraseList;

    /**
     * 灰度车辆名称列表
     */
    @LafValue(key = "mobile.command.grayVehicleNameList")
    private List<String> grayVehicleNameList;

    /**
     * 数据采集车辆名称列表
     */
    @LafValue(key = "mobile.data.collection.vehicleNameList")
    private List<String> dataCollVehicleNameList;

    /**
     * DUCC动态配置：数采车场景与标签关联值
     */
    @LafValue(key = "data.collection.overlap.value")
    private Integer dataCollectionOverlapValue;

    /**
     * DUCC动态配置：数采车提取关键字
     */
    @LafValue(key = "data.collection.key.words")
    private String dataCollectionKeyWords;

}