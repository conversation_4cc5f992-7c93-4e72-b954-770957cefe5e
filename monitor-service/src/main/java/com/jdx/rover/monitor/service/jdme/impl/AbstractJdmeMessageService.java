package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.monitor.dto.jdme.JdmeGroup;
import com.jdx.rover.monitor.dto.jdme.JdmeGroupUser;
import com.jdx.rover.monitor.dto.jdme.JueActionInfo;
import com.jdx.rover.monitor.dto.jdme.JueCardData;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButton;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonBehavior;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonHref;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonText;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementTextItem;
import com.jdx.rover.monitor.dto.jdme.JueCardDataEnums;
import com.jdx.rover.monitor.enums.mobile.NewAccidentLevelEnum;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.manager.ticket.IssueQueryApiManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import com.jdx.rover.shadow.api.domain.enums.EventSourceEnum;
import com.jdx.rover.shadow.api.domain.enums.ShadowTypeEnum;
import com.jdx.rover.shadow.api.domain.vo.ShadowSubscribeEventVO;
import com.jdx.rover.ticket.domain.dto.query.AlarmIssueDTO;
import com.jdx.rover.ticket.domain.vo.query.QueryAlarmIssueVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class AbstractJdmeMessageService {
    private IssueQueryApiManager issueQueryApiManager;
    private MetadataUserApiManager metadataUserApiManager;
    private AccidentJdmePushManager accidentJdmePushManager;
    protected MetadataVehicleApiManager metadataVehicleApiManager;
    protected String repairUrl;
    private String env;

    public AbstractJdmeMessageService() {
        issueQueryApiManager = SpringUtil.getBean(IssueQueryApiManager.class);
        metadataUserApiManager = SpringUtil.getBean(MetadataUserApiManager.class);
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        metadataVehicleApiManager = SpringUtil.getBean(MetadataVehicleApiManager.class);
        repairUrl = SpringUtil.getProperty("monitor-notify.monitorUrl");
        env = SpringUtil.getProperty("spring.profiles.active");
    }

    /**
     * 根据事故编号获取事故的远驾跟进人ERP
     * @param alarmNumber
     * @return
     */
    public String getRemoteDrivingFollowerErp(String alarmNumber) {
        if(StrUtil.isBlank(alarmNumber)) {
            log.info("根据事故编号获取事故的远驾跟进人ERP时发现告警事故编号为空");
            return null;
        }
        //根据事故编号获取工单信息中的远驾跟进人
        QueryAlarmIssueVO queryAlarmIssueVO = new QueryAlarmIssueVO();
        List<String> alarmNumberList = Lists.newArrayList(alarmNumber);
        queryAlarmIssueVO.setAlarmNoList(alarmNumberList);
        List<AlarmIssueDTO> issueInfoByAlarmList = issueQueryApiManager.getIssueInfoByAlarm(queryAlarmIssueVO);
        if(CollectionUtil.isEmpty(issueInfoByAlarmList)) {
            log.info("未找到事故编号[{}]对应的工单信息", alarmNumber);
            return null;
        }
        String remoteDrivingFollowerUsername = issueInfoByAlarmList.get(0).getAcceptUsername();
        if(StrUtil.isBlank(remoteDrivingFollowerUsername)) {
            log.info("事故编号[{}]对应工单尚无远驾跟进人！", alarmNumber);
            return null;
        }
        //返回远驾跟进人的ERP
        return getUserErpByName(remoteDrivingFollowerUsername);
    }

    /**
     * 根据用户名获取用户ERP
     * @param userName
     * @return
     */
    public String getUserErpByName(String userName) {
        UserExtendInfoDTO userInfo = metadataUserApiManager.getUserExtendInfoByName(userName);
        if(null == userInfo) {
            log.info("根据用户名获取用户信息为空，用户名[{}]", userName);
            return null;
        }

        return userInfo.getJdErp();
    }

    /**
     *
     * @param jdmePush
     * @return
     */
    public JdmeGroup buildJdmeGroup(AccidentJdmePush jdmePush, Accident accident) {
        List<JdmeGroupUser> members = new ArrayList<>();
        //远驾跟进人
        String remoteDrivingFollowerErp = getRemoteDrivingFollowerErp(accident.getAlarmNumber());
        if(StrUtil.isNotBlank(remoteDrivingFollowerErp)) {
            JdmeGroupUser remoteDrivingFollower = new JdmeGroupUser();
            remoteDrivingFollower.setPin(remoteDrivingFollowerErp);
            members.add(remoteDrivingFollower);
        }
        //一线跟进人
        if (accident.getOperationUser() != null) {
            String endFollowerErp = getUserErpByName(accident.getOperationUser());
            if(StrUtil.isNotBlank(endFollowerErp)) {
                JdmeGroupUser endFollower = new JdmeGroupUser();
                endFollower.setPin(endFollowerErp);
                members.add(endFollower);
            }
        }
        //问题跟进人
        if(StrUtil.isNotBlank(jdmePush.getFollower())) {
            JdmeGroupUser issueFollower = new JdmeGroupUser();
            issueFollower.setPin(jdmePush.getFollower());
            members.add(issueFollower);
        }

        String createTime = DateUtil.format(accident.getAccidentReportTime(), "yyyy/MM/dd HH:mm:ss");
        String busTitle = "事故发生时间：" + createTime + "  事故编号：" + accident.getAccidentNo();

        JdmeGroup jdmeGroup = new JdmeGroup();
        jdmeGroup.setName("碰撞-" + jdmePush.getVehicleName());
        jdmeGroup.setNotice(busTitle);
        jdmeGroup.setIntro(busTitle);
        jdmeGroup.setUniqueKey(jdmePush.getAccidentNo() + "-" + IdUtil.nanoId());
//        jdmeGroup.setOwner(owner);
        jdmeGroup.setMembers(members);

        return jdmeGroup;
    }

    /**
     * 订阅影子事件<br>
     * 查询或订阅异常视频生成结果
     * @param accident 提报的异常信息
     * @throws Exception
     */
    public ShadowSubscribeEventTaskDTO subscribeShadowEvent(Accident accident) throws Exception {
        ShadowSubscribeEventVO event = new ShadowSubscribeEventVO();
        event.setVehicleName(accident.getVehicleName());
        event.setSource(EventSourceEnum.PC_MONITOR.getValue());
        event.setShadowType(ShadowTypeEnum.ACCIDENT.getValue());
        event.setEventId(accident.getShadowEventId());
        return this.accidentJdmePushManager.subscribeTrackingEventTask(event);
    }

    /**
     * 构建卡片摘要
     * @param jueCardData
     * @param accident
     * @return
     */
    public String buildSummary(JueCardData jueCardData, Accident accident) {
        String createTime = DateUtil.format(accident.getAccidentReportTime(), "yyyy/MM/dd HH:mm:ss");
        String splitText = "  ";

        StringBuilder summarySb = new StringBuilder();
        summarySb.append("标题：" + jueCardData.getHeader().getTitle().getContent());
        summarySb.append(splitText);
        summarySb.append("问题描述：" + accident.getAccidentDesc());
        summarySb.append(splitText);
        summarySb.append("发生时间：" + createTime);
        summarySb.append(splitText);
        summarySb.append("事故编号：" + accident.getAccidentNo());

        return summarySb.toString();
    }

    /**
     * 将指定成员ERP添加到群
     * @param groupId 群号
     * @param erp 直接ERP
     * @param memberUsers 成员ERP
     */
    public void addMemberIntoGroup(String groupId, String erp, String... memberUsers) {
        List<JdmeGroupUser> memberList = new ArrayList<>();
        //直接ERP用户添加
        if(StrUtil.isNotBlank(erp)) {
            JdmeGroupUser member = new JdmeGroupUser();
            member.setPin(erp);
            memberList.add(member);
        }
        //成员用户添加，需要翻译用户ERP
        for(String memberUser : memberUsers) {
            if(StrUtil.isBlank(memberUser)) {
                log.info("向群[{}]添加成员时发现成员用户名为空！", groupId);
                continue;
            }
            String userErp = getUserErpByName(memberUser);
            if (StrUtil.isBlank(userErp)) {
                log.info("向群[{}]添加成员[{}]时发现成员ERP为空！", groupId, memberUser);
                continue;
            }
            JdmeGroupUser member = new JdmeGroupUser();
            member.setPin(userErp);
            memberList.add(member);
        }
        if(CollectionUtil.isNotEmpty(memberList)) {
            boolean result = accidentJdmePushManager.addGroupMember(memberList, groupId);
            log.info("安全组编辑处理时拉用户[{}]进群[{}],[{}]", memberUsers, groupId, result ? "成功" : "失败");
        }
    }

    public void addMemberIntoGroupForThirdVehicle(String groupId, String userName, List<String> erpList) {
        List<JdmeGroupUser> memberList = new ArrayList<>();
        if(StrUtil.isNotBlank(userName)){
            String userErp = getUserErpByName(userName);
            if (StrUtil.isNotBlank(userErp)) {
                JdmeGroupUser member = new JdmeGroupUser();
                member.setPin(userErp);
                memberList.add(member);
            }
        }
        if(CollectionUtil.isNotEmpty(erpList)) {
            for (String erp : erpList) {
                JdmeGroupUser member = new JdmeGroupUser();
                member.setPin(erp);
                memberList.add(member);
            }
        }
        if(CollectionUtil.isNotEmpty(memberList)) {
            boolean result = accidentJdmePushManager.addGroupMember(memberList, groupId);
            log.info("三方车处理事故时拉用户[{}]进群[{}],[{}]", erpList, groupId, result ? "成功" : "失败");
        }
    }

    /**
     * 获取咨询人工按钮
     * @param jdmeConfig
     * @return
     */
    public JueCardDataButton getManualButton(JdmeConfig jdmeConfig) {
        //咨询人工按钮
        JueCardDataElementTextItem manualButtonTextItem = new JueCardDataElementTextItem();
        manualButtonTextItem.setLabel("咨询人工");

        JueCardDataButtonText manualButtonText = new JueCardDataButtonText();
        manualButtonText.setContent(manualButtonTextItem);

        JueCardDataButtonHref manualButtonHref = new JueCardDataButtonHref();
        manualButtonHref.setPc("timline://chat/?topin=" + jdmeConfig.getRobot().getManualPin());
        manualButtonHref.setMobile("jdme://jm/biz/im/contact/details?mparam={\"erp\":\"" + jdmeConfig.getRobot().getManualPin() + "\"}");

        JueCardDataButton manualButton = new JueCardDataButton();
        manualButton.setType(JueCardDataEnums.ButtonType.DEFAULT.getCode());
        manualButton.setEnable(true);
        manualButton.setText(manualButtonText);
        manualButton.setHref(manualButtonHref);
        return manualButton;
    }

    /**
     * 获取加入处理群按钮
     * @param groupId
     * @return
     */
    public JueCardDataButton getAddGroupButton(String groupId) {
        JueCardDataElementTextItem processButtonTextItem = new JueCardDataElementTextItem();
        processButtonTextItem.setLabel("加入处理群");

        JueCardDataButtonText processButtonText = new JueCardDataButtonText();
        processButtonText.setContent(processButtonTextItem);

        JueCardDataButtonHref processButtonHref = new JueCardDataButtonHref();
        processButtonHref.setPc("");
        processButtonHref.setMobile("");

        JueCardDataButtonBehavior processButtonBehavior = new JueCardDataButtonBehavior();
        processButtonBehavior.setMethod(JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT);
        processButtonBehavior.setParams(groupId);

        JueCardDataButton processButton = new JueCardDataButton();
        processButton.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
        processButton.setEnable(true);
        processButton.setText(processButtonText);
        processButton.setHref(processButtonHref);
        processButton.setBehavior(processButtonBehavior);
        return processButton;
    }

    /**
     * 获取恢复运营按钮
     * @param businessType 业务类型
     * @param operatorType 操作类型
     * @param number 编号
     * @return 恢复运营按钮
     */
    public JueCardDataButton getRecoverButton(String businessType, String operatorType, String number) {
        JueCardDataElementTextItem recoverButtonTextItem = new JueCardDataElementTextItem();
        recoverButtonTextItem.setLabel("恢复运营");
        JueCardDataButtonText recoverButtonText = new JueCardDataButtonText();
        recoverButtonText.setContent(recoverButtonTextItem);
        JueActionInfo recoverActionInfo = new JueActionInfo();
        recoverActionInfo.setBusinessType(businessType);
        recoverActionInfo.setOperatorType(operatorType);
        recoverActionInfo.setNumber(number);
        recoverActionInfo.setEnv(env);
        JueCardDataButton recoverButton = new JueCardDataButton();
        recoverButton.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
        recoverButton.setEnable(true);
        recoverButton.setText(recoverButtonText);
        recoverButton.setAction(recoverActionInfo);
        return  recoverButton;
    }

    /**
     * 根据事故等级确定主题颜色
     * @param level 事故等级
     * @return 主题颜色代码
     */
    public String determineThemeColor(String level) {
        if (StrUtil.isBlank(level)) {
            return JueCardDataEnums.LabelColorType.GRAY.getCode();
        }

        // 高风险等级：S1-S4 使用红色
        if (isHighRiskLevel(level)) {
            return JueCardDataEnums.LabelColorType.RED.getCode();
        }

        // S0等级使用黄色
        if (NewAccidentLevelEnum.S0.getValue().equals(level)) {
            return JueCardDataEnums.LabelColorType.YELLOW.getCode();
        }

        // 默认灰色
        return JueCardDataEnums.LabelColorType.GRAY.getCode();
    }

    /**
     * 判断是否为高风险等级
     * @param level 事故等级
     * @return 是否为高风险等级
     */
    private boolean isHighRiskLevel(String level) {
        return NewAccidentLevelEnum.S1.getValue().equals(level) ||
                NewAccidentLevelEnum.S2.getValue().equals(level) ||
                NewAccidentLevelEnum.S3.getValue().equals(level) ||
                NewAccidentLevelEnum.S4.getValue().equals(level);
    }
}
