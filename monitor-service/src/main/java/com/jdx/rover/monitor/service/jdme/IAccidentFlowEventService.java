package com.jdx.rover.monitor.service.jdme;

/**
 * 事故流程事件处理器
 */
public interface IAccidentFlowEventService {
    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operateType 操作类型
     * @param operator 操作员
     * @param repeated 是事重试消息
     */
    public void handleMessage(String accidentNo, String accidentFlowType, String operateType, String operator, boolean repeated) throws Exception;
}
