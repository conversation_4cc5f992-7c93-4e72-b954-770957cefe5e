package com.jdx.rover.monitor.service.config.ducc;

import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * @description: 事故相关ducc配置类
 * @author: douyanghui
 * @date: 2025/8/21
 */

@Component
@Data
public class DuccAccidentProperties {

    /**
     * <p>
     * 三方车事故群人员列表
     * </p>
     */
    @LafValue("monitor.worker.accident.operateGroupList")
    private List<String> operateGroupList;
}
