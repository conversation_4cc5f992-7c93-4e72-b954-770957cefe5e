package com.jdx.rover.monitor.service.jdme;

import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.service.jdme.impl.*;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 京ME推送服务
 */
public class JdmeMessageServiceFactory {
    private static final Map<AccidentFlowEnum, IAccidentFlowEventService> handlers = new ConcurrentHashMap<>(9);

    /**
     * 创建具体推送服务
     * @param accidentFlowEnum
     * @return
     */
    public static IAccidentFlowEventService create(AccidentFlowEnum accidentFlowEnum) {
        return handlers.get(accidentFlowEnum);
    }

    static {
        handlers.put(AccidentFlowEnum.VEHICLE_REPORT, new VehicleReportService());
        handlers.put(AccidentFlowEnum.MANUALLY_CREATED, new ManuallyCreatedService());
        handlers.put(AccidentFlowEnum.TECHNICAL_SUPPORT_EDIT, new TechnicalSupportEditService());
        handlers.put(AccidentFlowEnum.OPERATION_ACCEPT, new OperationAcceptService());
        handlers.put(AccidentFlowEnum.OPERATION_REJECT, new OperationRejectService());
        handlers.put(AccidentFlowEnum.OPERATION_SUBMIT, new OperationSubmitService());
        handlers.put(AccidentFlowEnum.BUG_REPORT, new BugReportService());
        handlers.put(AccidentFlowEnum.SAFETY_GROUP_EDIT, new SafetyGroupEditService());
        handlers.put(AccidentFlowEnum.JD_ACCIDENT_EDIT, new AccidentEditService());
        handlers.put(AccidentFlowEnum.THIRD_ACCIDENT_REPORT, new ThirdVehicleAccidentCardService());
    }
}
