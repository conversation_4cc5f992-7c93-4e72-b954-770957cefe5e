/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.vehicle;

import com.jdx.rover.monitor.dto.vehicle.VehicleSimInfoDTO;
import com.jdx.rover.monitor.dto.vehicle.MergedSimInfoDTO;
import com.jdx.rover.monitor.repository.redis.VehicleSimDataRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSimStatusRepository;
import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 车辆SIM卡信息缓存服务
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleSimInfoService {

    /**
     * 车辆SIM卡数据Repository
     */
    private final VehicleSimDataRepository vehicleSimDataRepository;

    /**
     * 车辆SIM卡状态Repository
     */
    private final VehicleSimStatusRepository vehicleSimStatusRepository;

    /**
     * 获取车辆完整的SIM卡信息
     * 将simData和simStatus的字段根据deviceId合并在一起，返回List<MergedSimInfoDTO>
     *
     * @param vehicleName 车辆名称
     * @return 合并的SIM卡信息列表
     */
    public List<MergedSimInfoDTO> getVehicleSimInfo(String vehicleName) {
        return getVehicleSimInfoMerged(vehicleName);
    }

    /**
     * 获取车辆原始的SIM卡信息（保持向后兼容）
     *
     * @param vehicleName 车辆名称
     * @return 车辆SIM卡信息DTO
     */
    public VehicleSimInfoDTO getVehicleSimInfoOriginal(String vehicleName) {
        VehicleSimInfoDTO vehicleSimInfo = new VehicleSimInfoDTO();
        vehicleSimInfo.setVehicleName(vehicleName);

        List<SimDataDTO> simDataDTOS = vehicleSimDataRepository.get(vehicleName);
        List<SimStatusDTO> simStatusDTOS = vehicleSimStatusRepository.get(vehicleName);

        vehicleSimInfo.setSimDataList(simDataDTOS);
        vehicleSimInfo.setSimStatusList(simStatusDTOS);
        return vehicleSimInfo;
    }

    /**
     * 获取车辆合并的SIM卡信息列表
     * 将simData和simStatus的字段根据deviceId合并在一起
     *
     * @param vehicleName 车辆名称
     * @return 合并的SIM卡信息列表
     */
    public List<MergedSimInfoDTO> getVehicleSimInfoMerged(String vehicleName) {
        if (vehicleName == null) {
            log.warn("获取车辆SIM卡信息失败：车辆名称为空");
            return new ArrayList<>();
        }

        List<SimDataDTO> simDataList = vehicleSimDataRepository.get(vehicleName);
        List<SimStatusDTO> simStatusList = vehicleSimStatusRepository.get(vehicleName);

        // 如果数据为空，返回空列表
        if (simDataList == null && simStatusList == null) {
            log.debug("车辆 {} 的SIM卡数据和状态信息都为空", vehicleName);
            return new ArrayList<>();
        }

        // 使用Map来根据deviceId合并数据
        Map<Integer, MergedSimInfoDTO> mergedMap = new HashMap<>();

        // 处理SIM数据
        if (simDataList != null) {
            for (SimDataDTO simData : simDataList) {
                if (simData.getDeviceId() != null) {
                    MergedSimInfoDTO merged = mergedMap.computeIfAbsent(simData.getDeviceId(),
                        k -> new MergedSimInfoDTO());

                    // 复制SimDataDTO的字段
                    merged.setVehicleName(simData.getVehicleName());
                    merged.setDeviceId(simData.getDeviceId());
                    merged.setStandard(simData.getStandard());
                    merged.setBand(simData.getBand());
                    merged.setCommunityId(simData.getCommunityId());
                    merged.setStationId(simData.getStationId());
                    merged.setRsrp(simData.getRsrp());
                    merged.setSinr(simData.getSinr());
                    merged.setDailyUpload(simData.getDailyUpload());
                    merged.setDailyDownload(simData.getDailyDownload());
                    merged.setDataRecordTime(simData.getRecordTime());
                }
            }
        }

        // 处理SIM状态
        if (simStatusList != null) {
            for (SimStatusDTO simStatus : simStatusList) {
                if (simStatus.getDeviceId() != null) {
                    MergedSimInfoDTO merged = mergedMap.computeIfAbsent(simStatus.getDeviceId(),
                        k -> new MergedSimInfoDTO());

                    // 复制SimStatusDTO的字段
                    if (merged.getVehicleName() == null) {
                        merged.setVehicleName(vehicleName);
                    }
                    merged.setDeviceId(simStatus.getDeviceId());
                    merged.setOnlineStatus(simStatus.getOnlineStatus());
                    merged.setOnlineTime(simStatus.getOnlineTime());
                    merged.setOfflineTime(simStatus.getOfflineTime());
                    merged.setMainCard(simStatus.getMainCard());
                    merged.setInUse(simStatus.getInUse());
                    merged.setStatusRecordTime(simStatus.getRecordTime());
                }
            }
        }

        List<MergedSimInfoDTO> result = new ArrayList<>(mergedMap.values());
        log.debug("车辆 {} 合并后的SIM卡信息数量: {}", vehicleName, result.size());

        return result;
    }
}
