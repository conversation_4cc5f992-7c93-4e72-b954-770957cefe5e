/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.vehicle;

import com.jdx.rover.monitor.dto.vehicle.VehicleSimInfoDTO;
import com.jdx.rover.monitor.repository.redis.VehicleSimDataRepository;
import com.jdx.rover.monitor.repository.redis.VehicleSimStatusRepository;
import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.status.SimStatusDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆SIM卡信息缓存服务
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleSimInfoService {

    /**
     * 车辆SIM卡数据Repository
     */
    private final VehicleSimDataRepository vehicleSimDataRepository;

    /**
     * 车辆SIM卡状态Repository
     */
    private final VehicleSimStatusRepository vehicleSimStatusRepository;

    /**
     * 获取车辆完整的SIM卡信息
     *
     * @param vehicleName 车辆名称
     * @return 车辆SIM卡信息DTO
     */
    public VehicleSimInfoDTO getVehicleSimInfo(String vehicleName) {
        VehicleSimInfoDTO vehicleSimInfo = new VehicleSimInfoDTO();
        vehicleSimInfo.setVehicleName(vehicleName);

        List<SimDataDTO> simDataDTOS = vehicleSimDataRepository.get(vehicleName);
        List<SimStatusDTO> simStatusDTOS = vehicleSimStatusRepository.get(vehicleName);

        vehicleSimInfo.setSimDataList(simDataDTOS);
        vehicleSimInfo.setSimStatusList(simStatusDTOS);
        return vehicleSimInfo;
    }
}
