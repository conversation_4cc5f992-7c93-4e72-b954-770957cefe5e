/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import com.jdx.rover.metadata.jsf.service.box.MetadataBoxBasicService;
import com.jdx.rover.metadata.jsf.service.cockpit.MetadataCockpitDataService;
import com.jdx.rover.metadata.jsf.service.cockpit.MetadataCockpitInfoService;
import com.jdx.rover.metadata.jsf.service.deploy.DeviceInsuranceJsfService;
import com.jdx.rover.metadata.jsf.service.deployment.DeploymentMapTaskJsfService;
import com.jdx.rover.metadata.jsf.service.mobile.MetadataVehicleMobileJsfService;
import com.jdx.rover.metadata.jsf.service.require.MetadataRequireJsfService;
import com.jdx.rover.metadata.jsf.service.station.MetadataStationBasicService;
import com.jdx.rover.metadata.jsf.service.stop.MetadataStopBasicService;
import com.jdx.rover.metadata.jsf.service.technical.MetadataErrorCodeTranslateInfoService;
import com.jdx.rover.metadata.jsf.service.user.MetadataUserBasicService;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleBasicService;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleExceptionService;
import com.jdx.rover.metadata.jsf.service.vehicle.MetadataVehicleTypeBasicService;
import com.jdx.rover.metadata.jsf.service.warehouse.WarehouseBusinessJsfService;
import com.jdx.rover.metadata.jsf.service.word.MetadataWordMonitorService;
import com.jdx.rover.permission.jsf.service.basic.PermissionUserInfoBasicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 基础服务jsf配置
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Configuration
@Component
public class MetadataJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 MetadataBoxBasicService
     */
    @Bean
    public MetadataBoxBasicService metadataBoxBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataBoxBasicService.class).refer();
    }

    /**
     * 注册 MetadataCockpitInfoService
     */
    @Bean
    public MetadataCockpitInfoService metadataCockpitInfoService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataCockpitInfoService.class).refer();
    }

    /**
     * 注册 MetadataCockpitDataService
     */
    @Bean
    public MetadataCockpitDataService metadataCockpitDataService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataCockpitDataService.class).refer();
    }

    /**
     * 注册 MetadataErrorCodeTranslateInfoService
     */
    @Bean
    public MetadataErrorCodeTranslateInfoService metadataErrorCodeTranslateInfoService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataErrorCodeTranslateInfoService.class).refer();
    }

    /**
     * 注册 MetadataRequireJsfService
     */
    @Bean
    public MetadataRequireJsfService metadataRequireJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataRequireJsfService.class).refer();
    }

    /**
     * 注册 MetadataVehicleMobileJsfService
     */
    @Bean
    public MetadataVehicleMobileJsfService metadataVehicleMobileJsfService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataVehicleMobileJsfService.class).refer();
    }

    /**
     * 注册 MetadataStationBasicService
     */
    @Bean
    public MetadataStationBasicService metadataStationBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataStationBasicService.class).refer();
    }

    /**
     * 注册 MetadataStopBasicService
     */
    @Bean
    public MetadataStopBasicService metadataStopBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataStopBasicService.class).refer();
    }

    /**
     * 注册 PermissionUserInfoBasicService
     */
    @Bean
    public PermissionUserInfoBasicService permissionUserInfoBasicService() {
        return jsfConsumerRegister.createConsumerConfig(PermissionUserInfoBasicService.class).refer();
    }

    /**
     * 注册 MetadataUserBasicService
     */
    @Bean
    public MetadataUserBasicService metadataUserBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataUserBasicService.class).refer();
    }

    /**
     * 注册 MetadataVehicleBasicService
     */
    @Bean
    public MetadataVehicleBasicService metadataVehicleBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataVehicleBasicService.class).refer();
    }

    /**
     * 注册 MetadataVehicleExceptionService
     */
    @Bean
    public MetadataVehicleExceptionService metadataVehicleExceptionService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataVehicleExceptionService.class).refer();
    }

    /**
     * 注册 MetadataVehicleTypeBasicService
     */
    @Bean
    public MetadataVehicleTypeBasicService metadataVehicleTypeBasicService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataVehicleTypeBasicService.class).refer();
    }

    /**
     * 注册 MetadataWordMonitorService
     */
    @Bean
    public MetadataWordMonitorService metadataWordMonitorService() {
        return jsfConsumerRegister.createConsumerConfig(MetadataWordMonitorService.class).refer();
    }

    /**
     * 注册 DeviceInsuranceJsfService
     */
    @Bean
    public DeviceInsuranceJsfService deviceInsuranceJsfService() {
        return jsfConsumerRegister.createConsumerConfig(DeviceInsuranceJsfService.class).refer();
    }

    /**
     * 注册 WarehouseBusinessJsfService
     */
    @Bean
    public WarehouseBusinessJsfService warehouseBusinessJsfService() {
        return jsfConsumerRegister.createConsumerConfig(WarehouseBusinessJsfService.class).refer();
    }

    /**
     * 注册 DeploymentMapTaskJsfService
     */
    @Bean
    public DeploymentMapTaskJsfService deploymentMapTaskJsfService() {
        return jsfConsumerRegister.createConsumerConfig(DeploymentMapTaskJsfService.class).refer();
    }

}