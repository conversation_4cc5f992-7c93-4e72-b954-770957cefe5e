package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.amazonaws.HttpMethod;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.monitor.constant.AccidentConstant;
import com.jdx.rover.monitor.dto.jdme.*;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.accident.AccidentNewStatusEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperateEnum;
import com.jdx.rover.monitor.enums.mobile.NewAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentDetailManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentAttachment;
import com.jdx.rover.monitor.po.AccidentDetail;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.service.jdme.IAccidentFlowEventService;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 手动创建事故处理
 */
@Slf4j
public class ManuallyCreatedService extends AbstractJdmeMessageService implements IAccidentFlowEventService {
    private AccidentJdmePushManager accidentJdmePushManager;
    private AccidentManager accidentManager;
    private AccidentDetailManager accidentDetailManager;
    private VehicleManager monitorVehicleManager;
    private AccidentFlowLogManager accidentFlowLogManager;

    private AccidentAttachmentManager accidentAttachmentManager;

    private S3Properties s3Properties;

    public ManuallyCreatedService() {
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        accidentManager = SpringUtil.getBean(AccidentManager.class);
        accidentDetailManager = SpringUtil.getBean(AccidentDetailManager.class);
        monitorVehicleManager = SpringUtil.getBean(VehicleManager.class);
        accidentFlowLogManager = SpringUtil.getBean(AccidentFlowLogManager.class);
        accidentAttachmentManager = SpringUtil.getBean(AccidentAttachmentManager.class);
        s3Properties = SpringUtil.getBean(S3Properties.class);
    }

    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operator 操作员
     * @param repeated 重试消息
     */
    @Override
    public void handleMessage(String accidentNo, String accidentFlowType, String operateType, String operator, boolean repeated) throws Exception {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if(null == accident) {
            log.warn("车端上报事故处理未找到事故编号[{}]对应的事故信息,忽略处理！", accidentNo);
            return;
        }
        AccidentDetail accidentDetail = accidentDetailManager.selectByAccidentNo(accidentNo);
        if(null == accidentDetail) {
            log.warn("远程安全员编辑事故时未找到事故编号[{}]对应的事故详情,忽略处理！", accidentNo);
            return;
        }
        VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(accident.getVehicleName());
        List<AccidentFlowLog> flowLogList = accidentFlowLogManager.listByAccidentNo(accidentNo);
        String newGroupId = null;
        AccidentJdmePush accidentJdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        if(null == accidentJdmePush) {
            accidentJdmePush = buildAccidentJdmePush(accident, vehicleBasicDto);
        } else {
            newGroupId = accidentJdmePush.getAccidentGroupId();
        }
        accidentJdmePush.setLevel(accidentDetail.getNewAccidentLevel());
        accidentJdmePush.setTitle(accidentDetail.getTechnicalSupportDescription());

        //影子事件订阅
        try {
            ShadowSubscribeEventTaskDTO shadowEventTask = subscribeShadowEvent(accident);
            //持久化异常提报的需要发送的卡片消息
            if (null != shadowEventTask) {
                accidentJdmePush.setStatus(shadowEventTask.getStatus());
                accidentJdmePush.setVideoUrl(shadowEventTask.getVideoUrl());
                accidentJdmePush.setEventPlayUrl(shadowEventTask.getEventPlayUrl());
            }
        } catch (Exception e) {
            log.info("订阅影子事件[{}]订阅失败: {}", accident.getShadowEventId(), e);
        }

        if(StrUtil.isBlank(newGroupId)) {
            //根据当前事故编码创建新京ME群，从而获取到新群号，作为卡片消息的参数之一发送出去
            JdmeGroup jdmeGroup = buildJdmeGroup(accidentJdmePush, accident);
            newGroupId = accidentJdmePushManager.createGroup(jdmeGroup);
            accidentJdmePush.setAccidentGroupId(newGroupId);
        }
        accidentJdmePush.setAccidentFlowType(accidentFlowType);
        boolean bool = accidentJdmePushManager.saveOrUpdate(accidentJdmePush);
        log.info("京ME推送事故消息存储结果：{{}}", bool ? "成功" : "失败");

        //手动创建事故时自动进群
        StationBasicDTO stationBasicDTO = metadataVehicleApiManager.getStationInfoByVehicle(accident.getVehicleName());
        addMemberIntoGroup(newGroupId, null, operator, stationBasicDTO.getPersonName());

        JdmeConfig jdmeConfig = accidentJdmePushManager.getLatestConfig();
        JueCardData jueCardData = buildCardData(accident, accidentJdmePush, flowLogList);
        //给统一的领导群推送固定格式的卡片消息，卡片上的交互按钮的逻辑在此提前生成，按钮交互由前端JS完成
        jueCardData.setGroupId(jdmeConfig.getRobot().getFixedGroupId());
        jueCardData.setSummary(buildSummary(jueCardData, accident));
        //添加加入处理群按钮
        jueCardData.getButtons().getButtons().add(0, getAddGroupButton(newGroupId));
        //添加恢复按钮
        if(accident.getStatus().equals(AccidentNewStatusEnum.INFLUENCE_OPERATION.getValue())){
            jueCardData.getButtons().getButtons().add(getRecoverButton("accident", "recover", accident.getAccidentNo()));
        }
        if(!repeated) {
            this.accidentJdmePushManager.sendJUEMsg(jueCardData);
        }

        //给新群推送卡片消息，卡片内容相同，取消加入处理群
        jueCardData.setGroupId(newGroupId);
        jueCardData.getButtons().getButtons().remove(0);
        //添加咨询人工按钮
        jueCardData.getButtons().getButtons().add(0, getManualButton(jdmeConfig));
        this.accidentJdmePushManager.sendJUEMsg(jueCardData);

    }

   /**
     * 构建卡片数据
     * @param jdmePush 推送消息
     * @param flowLogList 事故流程记录
     * @return
     */
    private JueCardData buildCardData(Accident accident, AccidentJdmePush jdmePush,
                                      List<AccidentFlowLog> flowLogList) {
        //组装卡片消息头
        StringBuilder titleSb = new StringBuilder("【事故分析】");
        titleSb.append("【").append(AccidentNewStatusEnum.getNameByValue(accident.getStatus())).append("】");
        titleSb.append(SupplierEnum.getNameByValue(accident.getSupplier())).append("-");
        titleSb.append(jdmePush.getVehicleName());
        if(StrUtil.isNotBlank(jdmePush.getLevel())) {
            String level = NewAccidentLevelEnum.getNameByValue(jdmePush.getLevel());
            if(null != level) {
                titleSb.append("-");
                titleSb.append(level);
            } else {
                log.warn("手动创建事故处理时，事故等级[{}]无法识别！", jdmePush.getLevel());
            }
        }

        JueCardDataHeaderTitle headerTitle = new JueCardDataHeaderTitle();
        headerTitle.setContent(titleSb.toString());

        JueCardDataHeader header = new JueCardDataHeader();
        // 根据事故等级设置主题颜色
        String themeColor = determineThemeColor(jdmePush.getLevel());
        header.setTheme(themeColor);
        header.setTitle(headerTitle);

        //组装卡片消息体
        List<JueCardDataElementText> elementList = new ArrayList<>();
        //问题描述
        if(StrUtil.isNotBlank(jdmePush.getTitle())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("问题描述", jdmePush.getTitle(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //缺陷编号
        if(StrUtil.isNotBlank(jdmePush.getBugCode())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("缺陷编号", "http://xingyun.jd.com/test-manage/jbug/" + jdmePush.getBugId(), jdmePush.getBugCode(), JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //发生时间
        if(StrUtil.isNotBlank(jdmePush.getDebugTime())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生时间", jdmePush.getDebugTime(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //发生位置
        if(StrUtil.isNotBlank(jdmePush.getAccidentAddress())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("发生位置", jdmePush.getAccidentAddress(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //站点名称
        if(StrUtil.isNotBlank(accident.getStationName())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("站点名称", accident.getStationName(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //rover版本
        if(StrUtil.isNotBlank(accident.getRoverVersion())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("Rover版本", accident.getRoverVersion(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故快照
        List<AccidentAttachment> accidentAttachments = accidentAttachmentManager.selectOrderedListByAccidentNoAndSource(accident.getAccidentNo(), AccidentAttachmentSourceEnum.VIDEO_SNAPSHOT.getValue());
        List<String> snapshotUrlList = new ArrayList<>();
        for (AccidentAttachment accidentAttachment : accidentAttachments) {
            final Date expiration = DateUtil.offsetDay(new Date(), AccidentConstant.VIDEO_EXPIRATION);
            String videoUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), accidentAttachment.getBucket(), accidentAttachment.getFileKey(), HttpMethod.GET, expiration).toString();
            snapshotUrlList.add(videoUrl);
        }
        //事故视频
        if(StrUtil.isNotBlank(jdmePush.getVideoUrl())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故视频", jdmePush.getVideoUrl(), "查看", JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //PC端整车回放
        if(StrUtil.isNotBlank(jdmePush.getEventPlayUrl())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("整车回放", jdmePush.getEventPlayUrl(), "PC端查看", JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }
        //事故进度
        if(CollectionUtil.isNotEmpty(flowLogList)) {
            StringBuilder sb = new StringBuilder("\n");
            for(int i = 0; i < flowLogList.size(); i++) {
                AccidentFlowLog log = flowLogList.get(i);
                sb.append(i + 1);
                sb.append("、");
                sb.append(log.getContent());
                if(i < flowLogList.size() - 1) {
                    sb.append("\n");
                }
            }

            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故进度", sb.toString(), "", JueCardDataEnums.ElementValueType.TEXT));
            elementList.add(elementText);
        }
        //事故负责人
        if(StrUtil.isNotBlank(jdmePush.getFollower())) {
            JueCardDataElementText elementText = new JueCardDataElementText();
            elementText.setContent(new JueCardDataElementTextItem("事故负责人", jdmePush.getFollower() + "/ee", jdmePush.getFollower(),  JueCardDataEnums.ElementValueType.LINK));
            elementList.add(elementText);
        }

        //维修单入口
        JueCardDataElementText elementText = new JueCardDataElementText();
        String url = repairUrl + accident.getVehicleName() + "&autoOpenRepairModal=1";
        elementText.setContent(new JueCardDataElementTextItem("报修入口", url, "PC端操作", JueCardDataEnums.ElementValueType.LINK));
        elementList.add(elementText);

        //组装卡片消息按钮
        List<JueCardDataButton> buttonList = new ArrayList<>();
        JueCardDataButtons buttons = new JueCardDataButtons();
        buttons.setLayout(JueCardDataEnums.ButtonLayout.ROW);
        buttons.setButtons(buttonList);

        JueCardData jueCardData = new JueCardData();
        jueCardData.setHeader(header);
        jueCardData.setElements(elementList);
        jueCardData.setSnapshotUrlList(snapshotUrlList);
        jueCardData.setButtons(buttons);

        return jueCardData;
    }

    private AccidentJdmePush buildAccidentJdmePush(Accident accident, VehicleBasicDTO vehicleBasicDto) {
        //构建持久化对象
        String createTime = DateUtil.format(accident.getAccidentReportTime(), "yyyy/MM/dd HH:mm:ss");
        AccidentJdmePush jdmePush = new AccidentJdmePush();
        jdmePush.setShadowEventId(accident.getShadowEventId());
        jdmePush.setAccidentNo(accident.getAccidentNo());
//        jdmePush.setTitle("事故发生时间：" + createTime + "  事故编号：" + accident.getAccidentNo());
        jdmePush.setVehicleName(accident.getVehicleName());
        jdmePush.setBugCode(accident.getBugCode());
        jdmePush.setDebugTime(createTime);
        jdmePush.setAccidentAddress(accident.getAccidentAddress());
        jdmePush.setCreateUser(accident.getCreateUser());
        jdmePush.setModifyUser(accident.getModifyUser());
        return jdmePush;
    }
}
