package com.jdx.rover.monitor.service.jdme.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jdx.rover.monitor.dto.jdme.*;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.accident.AccidentAttachmentManager;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.service.jdme.IAccidentFlowEventService;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 车端上报事故处理
 */
@Slf4j
public class VehicleReportService extends AbstractJdmeMessageService implements IAccidentFlowEventService {
    private AccidentJdmePushManager accidentJdmePushManager;
    private AccidentManager accidentManager;
    private VehicleManager monitorVehicleManager;
    private AccidentFlowLogManager accidentFlowLogManager;

    public VehicleReportService() {
        accidentJdmePushManager = SpringUtil.getBean(AccidentJdmePushManager.class);
        accidentManager = SpringUtil.getBean(AccidentManager.class);
        monitorVehicleManager = SpringUtil.getBean(VehicleManager.class);
        accidentFlowLogManager = SpringUtil.getBean(AccidentFlowLogManager.class);
    }

    /**
     * 处理消息
     * @param accidentNo 事故号
     * @param accidentFlowType 事故处理环节
     * @param operator 操作员
     * @param repeated 重试消息
     */
    @Override
    public void handleMessage(String accidentNo, String accidentFlowType, String operateType, String operator, boolean repeated) throws Exception {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if(null == accident) {
            log.warn("车端上报事故处理未找到事故编号[{}]对应的事故信息,忽略处理！", accidentNo);
            return;
        }
        VehicleBasicDTO vehicleBasicDto = monitorVehicleManager.getBasicByName(accident.getVehicleName());
        AccidentJdmePush accidentJdmePush = accidentJdmePushManager.getByEventId(accident.getShadowEventId());
        if(null == accidentJdmePush) {
            accidentJdmePush = buildAccidentJdmePush(accident, vehicleBasicDto);
        }
        //影子事件订阅
        try {
            ShadowSubscribeEventTaskDTO shadowEventTask = subscribeShadowEvent(accident);
            //持久化异常提报的需要发送的卡片消息
            if (null != shadowEventTask) {
                accidentJdmePush.setStatus(shadowEventTask.getStatus());
                accidentJdmePush.setVideoUrl(shadowEventTask.getVideoUrl());
                accidentJdmePush.setEventPlayUrl(shadowEventTask.getEventPlayUrl());
            }
        } catch (Exception e) {
            log.info("订阅影子事件[{}]订阅失败: {}", accident.getShadowEventId(), e);
        }

        accidentJdmePush.setAccidentFlowType(accidentFlowType);
        boolean bool = accidentJdmePushManager.saveOrUpdate(accidentJdmePush);
        log.info("京ME推送事故消息存储结果：{{}}", bool ? "成功" : "失败");

    }

    private AccidentJdmePush buildAccidentJdmePush(Accident accident, VehicleBasicDTO vehicleBasicDto) {
        //构建持久化对象
        String createTime = DateUtil.format(accident.getAccidentReportTime(), "yyyy/MM/dd HH:mm:ss");
        AccidentJdmePush jdmePush = new AccidentJdmePush();
        jdmePush.setShadowEventId(accident.getShadowEventId());
        jdmePush.setAccidentNo(accident.getAccidentNo());
//        jdmePush.setTitle("事故发生时间：" + createTime + "  事故编号：" + accident.getAccidentNo());
        jdmePush.setVehicleName(accident.getVehicleName());
        jdmePush.setBugCode(accident.getBugCode());
        jdmePush.setDebugTime(createTime);
        jdmePush.setAccidentAddress(accident.getAccidentAddress());
        jdmePush.setCreateUser(accident.getCreateUser());
        jdmePush.setModifyUser(accident.getModifyUser());
        return jdmePush;
    }
}
