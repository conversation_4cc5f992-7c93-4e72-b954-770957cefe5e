/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.robot.alarm;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.vo.devicecommand.ImmediateTaskCreateVO;
import com.jdx.rover.device.jsfapi.service.server.devicecommand.IntelligentDeviceServerDeviceCommandTaskInfoService;
import com.jdx.rover.monitor.api.domain.dto.AlarmInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.VehicleAlarmChangeDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceAbnormalInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotHmiAlarmDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.entity.device.DeviceStatusChangeDO;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.device.*;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.robot.RobotAbnormalInfoManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.pda.DeviceBatchUpdateStatusRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.robot.ProductTypeEnum;
import com.jdx.rover.monitor.service.robot.RobotIssueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机器人告警服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RobotAlarmService {
  /**
   * 机器人异常信息服务。
   */
  private final RobotAbnormalInfoManager robotAbnormalInfoManager;

  /**
   * 告警缓存
   */
  public final RobotAlarmRepository robotAlarmRepository;

  /**
   * 机器人调度信息服务。
   */
  private final RobotScheduleRepository robotScheduleRepository;

  /**
   * 机器人实时信息服务。
   */
  private final RobotRealtimeInfoManager robotRealtimeInfoManager;

  /**
   * 机器人工单信息服务。
   */
  private final RobotIssueService robotIssueService;

  /**
   * jmq发送服务
   */
  private final JmqProducerService jmqProducerService;

  /**
   * 设备状态服务接口
   * 获取指定设备的实时信息
   */
  private final TransportDeviceApiManager transportDeviceApiManager;

  /**
   * 批量更新实时状态对象
   */
  private final DeviceBatchUpdateStatusRepository deviceBatchUpdateRepository;

  /**
   * 指令服务
   */
  private final IntelligentDeviceServerDeviceCommandTaskInfoService commandTaskInfoService;

  /**
   * 处理机器人报警状态变更事件
   * @param robotAlarmChangeDto 机器人报警状态变更信息
   */
  public void handleRobotAlarmChange(VehicleAlarmChangeDTO robotAlarmChangeDto) {
    List<AlarmInfoDTO> currentAlarm = robotAlarmChangeDto.getCurrentAlarm();
    if (CollectionUtils.isEmpty(currentAlarm) || !currentAlarm.stream().anyMatch(alarmInfoDTO -> StringUtils.equalsAny(
            alarmInfoDTO.getErrorLevel(), AlarmLevelEnum.ALARM_URGENT.getValue(), AlarmLevelEnum.ALARM_MAJOR.getValue()))) {
      // 结束工单
      robotIssueService.endRobotIssue(ProductTypeEnum.INTEGRATE.getValue(), robotAlarmChangeDto.getVehicleName());
    } else {
      List<AlarmInfoDTO> issueAlarmList = currentAlarm.stream().filter(alarmInfoDTO -> StringUtils.equalsAny(
              alarmInfoDTO.getErrorLevel(), AlarmLevelEnum.ALARM_URGENT.getValue(), AlarmLevelEnum.ALARM_MAJOR.getValue())).collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(issueAlarmList)) {
        //新增工单
        robotIssueService.addRobotIssue(robotAlarmChangeDto.getVehicleName(), robotAlarmChangeDto.getCurrentAlarm());
      }
    }
    updateDeviceRealtimeState(robotAlarmChangeDto);
    sendHmiAlarmMessage(robotAlarmChangeDto, currentAlarm);
    sendUiAlarmMessage(robotAlarmChangeDto, currentAlarm);
    sendRobotMapPosition(robotAlarmChangeDto, currentAlarm);
  }

  /**
   * 添加设备异常信息。
   * @param deviceName 设备名称。
   * @param productType 产品类型。
   */
  public void addDeviceAbnormal(String deviceName, String productType, RobotAlarmDO.DeviceAlarmEventDO alarmEventDo) {
    RobotAbnormalInfo robotAbnormalInfo = new RobotAbnormalInfo();
    robotAbnormalInfo.setProductKey(productType);
    robotAbnormalInfo.setDeviceName(deviceName);
    robotAbnormalInfo.setBootId(0L);
    robotAbnormalInfo.setErrorCode(alarmEventDo.getErrorCode());
    robotAbnormalInfo.setErrorNumber(0);
    robotAbnormalInfo.setErrorMsg(alarmEventDo.getErrorMsg());
    if (Objects.nonNull(AlarmLevelEnum.of(alarmEventDo.getErrorLevel()))) {
      robotAbnormalInfo.setErrorLevel(alarmEventDo.getErrorLevel());
    }
    robotAbnormalInfo.setStartTime(new Date());
    robotAbnormalInfo.setAbnormalModule(alarmEventDo.getAbnormalModule());
    RobotRealtimeInfo deviceDetailDto = robotRealtimeInfoManager.getDeviceNyName(productType, deviceName);
    if (Objects.nonNull(deviceDetailDto)) {
      robotAbnormalInfo.setRemarkName(deviceDetailDto.getRemarkName());
      robotAbnormalInfo.setGroupNo(String.format("%s,%s,%s", deviceDetailDto.getGroupOne(), deviceDetailDto.getGroupTwo(), deviceDetailDto.getGroupThree()));
      robotAbnormalInfo.setStationName(deviceDetailDto.getGroupLevelName());
      robotAbnormalInfo.setWorkMode(deviceDetailDto.getWorkMode());
    }
    RobotScheduleDO robotScheduleDo = robotScheduleRepository.get(deviceName);
    if(Objects.nonNull(robotScheduleDo)) {
      robotAbnormalInfo.setTaskNo(robotScheduleDo.getTaskNo());
    }
    RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
    Map<String, Object> realtimeMap = transportDeviceApiManager.getDeviceStatusDetail(deviceName, DevicePropertyCodeEnum.ALL_STATUS.getPropertyList());
    BeanUtil.fillBeanWithMapIgnoreCase(realtimeMap, realtimeStateDto, true);
    if (Objects.nonNull(realtimeStateDto.getX()) && Objects.nonNull(realtimeStateDto.getY())) {
      robotAbnormalInfo.setPoint(realtimeStateDto.getX() + "," + realtimeStateDto.getY());
    }
    robotAbnormalInfoManager.save(robotAbnormalInfo);
  }

  /**
   * 更新设备异常信息的结束时间。
   * @param deviceName 设备名称。
   * @param productKey 产品密钥。
   * @param errorCode 异常代码。
   */
  public void endDeviceAbnormal(String deviceName, String productKey, String errorCode) {
    LambdaUpdateWrapper<RobotAbnormalInfo> updateWrapper = new LambdaUpdateWrapper();
    updateWrapper.eq(RobotAbnormalInfo::getDeviceName, deviceName);
    updateWrapper.eq(RobotAbnormalInfo::getProductKey, productKey);
    updateWrapper.eq(RobotAbnormalInfo::getErrorCode, errorCode);
    updateWrapper.isNull(RobotAbnormalInfo::getEndTime);
    updateWrapper.set(RobotAbnormalInfo::getEndTime, new Date());
    robotAbnormalInfoManager.update(updateWrapper);
  }

  /**
   * 发送机器人告警变化通知
   * @param deviceName 设备名称
   * @param alarmMapList 告警事件映射表
   */
  public void sendJmqMessage(String deviceName, Collection<DeviceAlarmEventDO> alarmMapList) {
    VehicleAlarmChangeDTO changeDto = new VehicleAlarmChangeDTO();
    changeDto.setVehicleName(deviceName);
    changeDto.setRecordTime(new Date());
    List<AlarmInfoDTO> currentAlarm = alarmMapList.stream().map(abnormal -> {
      AlarmInfoDTO alarmInfoDTO = new AlarmInfoDTO();
      DeviceAlarmCodeEnum alarmCodeEnum = DeviceAlarmCodeEnum.of(abnormal.getErrorCode());
      if (Objects.nonNull(alarmCodeEnum)) {
        alarmInfoDTO.setAlarmType(alarmCodeEnum.getAlarmCode());
      }
      alarmInfoDTO.setStartTime(abnormal.getReportTime());
      alarmInfoDTO.setErrorCode(abnormal.getErrorCode());
      alarmInfoDTO.setErrorLevel(abnormal.getErrorLevel());
      alarmInfoDTO.setErrorMessage(abnormal.getErrorMsg());
      return alarmInfoDTO;
    }).collect(Collectors.toList());
    changeDto.setCurrentAlarm(currentAlarm);
    log.info("发送机器人告警变化通知{}", JsonUtils.writeValueAsString(changeDto));
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ROBOT_EVENT_CHANGE.getTopic(), deviceName, changeDto);
  }

  /**
   * 更新设备实时状态。
   * @param robotAlarmChangeDto 车辆报警更改数据传输对象。
   */
  private void updateDeviceRealtimeState(VehicleAlarmChangeDTO robotAlarmChangeDto) {
    HashSet<DeviceStatusChangeDO> deviceList = new HashSet<>();
    DeviceStatusChangeDO changeDo = new DeviceStatusChangeDO();
    changeDo.setDeviceName(robotAlarmChangeDto.getVehicleName());
    changeDo.setProductKey(ProductTypeEnum.INTEGRATE.getValue());
    deviceList.add(changeDo);
    deviceBatchUpdateRepository.batchAdd(deviceList);
  }

  /**
   * 发送HMI报警消息。
   * @param robotAlarmChangeDto 车辆报警变化DTO对象。
   * @param currentAlarm 当前报警信息列表。
   */
  private void sendHmiAlarmMessage(VehicleAlarmChangeDTO robotAlarmChangeDto, List<AlarmInfoDTO> currentAlarm) {
    ImmediateTaskCreateVO createVo = new ImmediateTaskCreateVO();
    createVo.setDeviceName(robotAlarmChangeDto.getVehicleName());
    createVo.setProductKey(ProductTypeEnum.INTEGRATE.getValue());
    createVo.setBlockNo("hmi");
    createVo.setIdentifier(DeviceCommandTaskEnum.CMD_ALARM_UPDATE.getValue());
    RobotHmiAlarmDTO hmiAlarmDto = new RobotHmiAlarmDTO();
    hmiAlarmDto.setCurrentAlarm(new ArrayList<>());
    if (CollectionUtils.isNotEmpty(currentAlarm)) {
      hmiAlarmDto.setCurrentAlarm(currentAlarm.stream().filter(alarm -> StringUtils.equalsAny(
              alarm.getErrorLevel(), AlarmLevelEnum.ALARM_URGENT.getValue(), AlarmLevelEnum.ALARM_MAJOR.getValue())).map(
              alarmInfoDTO -> {
                RobotHmiAlarmDTO.AlarmInfo alarmInfo = new RobotHmiAlarmDTO.AlarmInfo();
                alarmInfo.setAlarmNumber(alarmInfoDTO.getAlarmNumber());
                alarmInfo.setTimestamp(alarmInfoDTO.getStartTime());
                DeviceAlarmCodeEnum alarmCodeEnum = DeviceAlarmCodeEnum.of(alarmInfoDTO.getErrorCode());
                if (Objects.nonNull(alarmCodeEnum)) {
                  alarmInfo.setAlarmTypeName(alarmCodeEnum.getAlarmMsg());
                  alarmInfo.setSolutionTypeName(alarmCodeEnum.getSolutionName());
                }
                return alarmInfo;
              }
      ).collect(Collectors.toList()));
    }
    createVo.setCommandArgs(JsonUtils.writeValueAsString(hmiAlarmDto));
    commandTaskInfoService.createImmediateTask(createVo);
  }

  /**
   * 发送前端用户告警消息
   * @param robotAlarmChangeDto 车辆告警变更数据传输对象，包含车辆名称和当前告警信息
   * @param currentAlarm 当前告警信息列表，包含告警代码、级别和类型
   */
  private void sendUiAlarmMessage(VehicleAlarmChangeDTO robotAlarmChangeDto, List<AlarmInfoDTO> currentAlarm) {
    String topicName = RedisTopicEnum.MAP_ROBOT_CONGESTION_PREFIX.getValue() + robotAlarmChangeDto.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    List<RobotDeviceAbnormalInfoDTO> abnormalInfoList = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(currentAlarm)) {
      currentAlarm.stream().forEach(alarmInfoDTO -> {
        RobotDeviceAbnormalInfoDTO robotDeviceAbnormalInfo = new RobotDeviceAbnormalInfoDTO();
        robotDeviceAbnormalInfo.setAlarmCode(alarmInfoDTO.getErrorCode());
        robotDeviceAbnormalInfo.setAlarmLevel(alarmInfoDTO.getErrorLevel());
        DeviceAlarmCodeEnum alarmCodeEnum = DeviceAlarmCodeEnum.of(alarmInfoDTO.getAlarmType());
        if (Objects.nonNull(alarmCodeEnum)) {
          robotDeviceAbnormalInfo.setAlarmCodeName(alarmCodeEnum.getAlarmMsg());
        }
        abnormalInfoList.add(robotDeviceAbnormalInfo);
      });
    }
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_ALARM.getValue(), abnormalInfoList);
    rTopic.publish(JsonUtils.writeValueAsString(wsResult));
  }

  /**
   * 发送车辆地图实时状态变化
   */
  private void sendRobotMapPosition(VehicleAlarmChangeDTO robotAlarmChangeDto, List<AlarmInfoDTO> currentAlarm) {
    RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
    Map<String, Object> realtimeMap = transportDeviceApiManager.getDeviceStatusDetail(robotAlarmChangeDto.getVehicleName(), DevicePropertyCodeEnum.ALL_STATUS.getPropertyList());
    BeanUtil.fillBeanWithMapIgnoreCase(realtimeMap, realtimeStateDto, true);
    RobotMapRealtimeInfoDTO vehicleMapInfoDto = new RobotMapRealtimeInfoDTO();
    vehicleMapInfoDto.setProductKey(realtimeStateDto.getProductKey());
    vehicleMapInfoDto.setDeviceName(robotAlarmChangeDto.getVehicleName());
    vehicleMapInfoDto.setX(realtimeStateDto.getX());
    vehicleMapInfoDto.setY(realtimeStateDto.getY());
    vehicleMapInfoDto.setOnlineStatus(DeviceRealtimeStateEnum.ONLINE.getProperty());
    vehicleMapInfoDto.setYaw(realtimeStateDto.getYaw());
    vehicleMapInfoDto.setAlarmState(DeviceAlarmCategoryEnum.NORMAL.getValue());
    if (CollectionUtils.isNotEmpty(currentAlarm)) {
      Optional<DeviceAlarmCategoryEnum> op = currentAlarm.stream().map(alarmEvent ->
              DeviceAlarmCategoryEnum.getByValue(DeviceAlarmCodeEnum.of(alarmEvent.getAlarmType()).getCategory())).filter(Objects::nonNull).sorted(Comparator.comparingInt(Enum::ordinal)).findFirst();
      op.ifPresent(alarmCategory -> vehicleMapInfoDto.setAlarmState(alarmCategory.getValue()));
    }
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_UPDATE.getValue(), vehicleMapInfoDto);
    String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + vehicleMapInfoDto.getDeviceName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.publish(JsonUtils.writeValueAsString(wsResult));
  }


}