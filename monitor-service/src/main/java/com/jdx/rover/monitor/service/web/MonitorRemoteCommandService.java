/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.service.web;

import com.atlassian.jira.rest.client.api.domain.LoginInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.api.domain.enums.CockpitTypeEnum;
import com.jdx.rover.monitor.api.domain.dto.VehicleTakeOverDTO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.dto.transport.UserOperateCommandDTO;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.MonitorKafkaTopicConstant;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mobile.H5RemotePowerEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.command.RemoteOperationTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.mobile.CommandManager;
import com.jdx.rover.monitor.manager.vehicle.PowerManagerManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.UserVehicleNameRepository;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.LocalViewCtrlVO;
import com.jdx.rover.monitor.vo.MonitorPostTrafficLightCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorResetAbnormalControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorVehiclePowerOnVO;
import com.jdx.rover.monitor.vo.MonitorXataChangeModeCommandVO;
import com.jdx.rover.monitor.vo.NoSignalIntersectionCommandVO;
import com.jdx.rover.monitor.vo.s3.S3PreSignVO;
import com.jdx.rover.schedule.jsf.schedule.ScheduleMonitorJsfService;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import com.jdx.rover.server.api.domain.enums.power.PowerManagerActionEnum;
import com.jdx.rover.server.api.domain.vo.LocalViewCtrlCommandVO;
import com.jdx.rover.server.api.domain.vo.PassNoSignalIntersectionCommandVO;
import com.jdx.rover.server.api.domain.vo.PowerManagerCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteControlCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteResetAbnormalCommandVO;
import com.jdx.rover.server.api.domain.vo.RemoteTrafficLightCommandVO;
import com.jdx.rover.server.api.domain.vo.SwitchVehicleModeCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * This is a vehicle remote command service.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class MonitorRemoteCommandService {

  /**
   * <p>
   * Represents the vehicle take over repository.
   * </p>
   */
  @Autowired
  private VehicleTakeOverRepository vehicleTakeOverRepository;
  @Autowired
  private VehicleRealtimeRepository vehicleRealtimeRepository;

  @Autowired
  private RemoteCommandService remoteCommandJsfService;

  @Autowired
  private JmqProducerService jmqProducerService;

  @Autowired
  private PowerManagerManager powerManagerManager;

  @Autowired
  private UserStatusRepository userStatusRepository;

  @Autowired
  private UserVehicleNameRepository userVehicleNameRepository;

  @Autowired
  private TrackingEventCollectService eventCollectService;

  @Autowired
  private KafkaTemplate<String, String> kafkaTemplate;

  @Autowired
  private ScheduleMonitorJsfService scheduleMonitorJsfService;

  @Autowired
  private CommandManager commandManager;

  /**
   * <p>
   * This is a inner class use as private functions' return data struct.
   * </p>
   */
  @Data
  private class ResultInfo {

    /**
     * <p>
     * Represents whether params meet the post conditions. The default value is
     * false. It's changeable.
     * </p>
     */
    private Boolean ableToPost;

    /**
     * <p>
     * Represents whether db has the record. The default value is false. It's
     * changeable.
     * </p>
     */
    private Boolean hasRecord;

    /**
     * <p>
     * Represents the name of user. The default value is null. It's changeable.
     * </p>
     */
    private String recordName;

    /**
     * <p>
     * Represents the source of command. The default value is null. It's changeable.
     * </p>
     */
    private String commandSource;

  }

  /**
   * <p>
   * 急停
   * </p>
   *
   * @param emergencyStopCommandVO the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postEmergencyStopRequest(MonitorRemoteCommandVO emergencyStopCommandVO) {
    ParameterCheckUtility.checkNotNull(emergencyStopCommandVO, "emergencyStopCommandVO");
    ParameterCheckUtility.checkNotNullNorEmpty(emergencyStopCommandVO.getVehicleName(),
            "emergencyStopCommandVO#vehicleName");
    ParameterCheckUtility.checkNotNull(emergencyStopCommandVO.getTimeStamp(), "emergencyStopCommandVO#timeStamp");
    log.info("Receive emergency command {}.", emergencyStopCommandVO);
    String userName = UserUtils.getAndCheckLoginUser();;
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue());
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(emergencyStopCommandVO.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(emergencyStopCommandVO.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(emergencyStopCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("急停失败!{},{}", result, emergencyStopCommandVO);
      return result;
    }
    UserStatusDO userStatusDo = userStatusRepository.get(userName);
    VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
    vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
    vehicleTakeOverEntity.setUserName(userName);
    if (!Objects.isNull(userStatusDo) && StringUtils.equals(userStatusDo.getWorkMode(), CockpitTypeEnum.MONITOR_SEAT.getValue())) {
      vehicleTakeOverEntity.setCockpitNumber(userStatusDo.getCockpitNumber());
    }
    vehicleTakeOverEntity.setOperateTime(new Date());
    vehicleTakeOverEntity.setCommandSource(RemoteCommandSourceEnum.MONITOR.getCommandSource());
    vehicleTakeOverRepository.save(emergencyStopCommandVO.getVehicleName(), vehicleTakeOverEntity);
    try {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(emergencyStopCommandVO),
                WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * 急刹
   * </p>
   *
   * @param emergencyBrakeCommandVo the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postEmergencyBrakeRequest(MonitorRemoteCommandVO emergencyBrakeCommandVo) {
    ParameterCheckUtility.checkNotNull(emergencyBrakeCommandVo, "monitorEmergencyBrakeCommandVo");
    ParameterCheckUtility.checkNotNull(emergencyBrakeCommandVo.getVehicleName(),
            "monitorEmergencyBrakeCommandVo#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive emergency brake request {}, userName {}.", emergencyBrakeCommandVo, userName);

    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(emergencyBrakeCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(emergencyBrakeCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(emergencyBrakeCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("急刹失败!{},{}", result, emergencyBrakeCommandVo);
      return result;
    }
    try {
        sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(emergencyBrakeCommandVo),
                WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue());
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Post recovery request.
   * </p>
   *
   * @param monitorRecoveryCommandVo the supervisor recovery request vo
   * @return The remote request response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   * @throws AppException             If failed to find object.
   */
  public HttpResult postRecoveryRequest(MonitorRemoteCommandVO monitorRecoveryCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorRecoveryCommandVo, "monitorRecoveryCommandVo");
    ParameterCheckUtility.checkNotNull(monitorRecoveryCommandVo.getVehicleName(),
            "monitorRecoveryCommandVo#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive recovery request {}, userName {}.", monitorRecoveryCommandVo, userName);

    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorRecoveryCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + monitorRecoveryCommandVo.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      log.error("Vehicle {} within control.", topicName);
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(),MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getMessage());
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue());
    vehicleTakeOverRepository.remove(monitorRecoveryCommandVo.getVehicleName());
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(monitorRecoveryCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(monitorRecoveryCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Post remote control request.
   * </p>
   *
   * @param monitorRemoteControlCommandVo the supervisor remote control request
   *                                         vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  @Transactional
  public WsResult postRemoteControlRequest(
          MonitorRemoteControlCommandVO monitorRemoteControlCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo, "monitorRemoteControlCommandVo");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getVehicleName(),
            "monitorRemoteControlCommandVo#vehicleName");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getUserName(),
            "monitorRemoteControlCommandVo#userName");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getTargetVelocity(),
            "monitorRemoteControlCommandVo#targetVelocity");
    ParameterCheckUtility.checkNotNull(monitorRemoteControlCommandVo.getTargetAngle(),
            "monitorRemoteControlCommandVo#targetAngle");
    log.info("Receive remote control request {}.", monitorRemoteControlCommandVo);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorRemoteControlCommandVo.getVehicleName(),
            monitorRemoteControlCommandVo.getUserName(), RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return WsResult.error(WebsocketEventTypeEnum.REMOTE_CONTROL_SESSION_CLOSE.getValue(),
              HttpCodeEnum.FORBIDDEN.getValue(),message);
    } else if (!resultInfo.getHasRecord()) {
      UserStatusDO userStatusDo = userStatusRepository.get(monitorRemoteControlCommandVo.getUserName());
      VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
      vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
      vehicleTakeOverEntity.setUserName(monitorRemoteControlCommandVo.getUserName());
      if (!Objects.isNull(userStatusDo)) {
        vehicleTakeOverEntity.setCockpitNumber(userStatusDo.getCockpitNumber());
      }
      vehicleTakeOverEntity.setOperateTime(new Date());
      vehicleTakeOverEntity.setCommandSource(RemoteCommandSourceEnum.MONITOR.getCommandSource());
      vehicleTakeOverRepository.save(monitorRemoteControlCommandVo.getVehicleName(), vehicleTakeOverEntity);
    }
    RemoteControlCommandVO remoteControlCommandVo = new RemoteControlCommandVO();
    remoteControlCommandVo.setVehicleName(monitorRemoteControlCommandVo.getVehicleName());
    remoteControlCommandVo.setId(monitorRemoteControlCommandVo.getId());
    remoteControlCommandVo.setModuleName(monitorRemoteControlCommandVo.getModuleName());
    remoteControlCommandVo.setTargetAngle(monitorRemoteControlCommandVo.getTargetAngle());
    remoteControlCommandVo.setTargetVelocity(monitorRemoteControlCommandVo.getTargetVelocity());
    remoteControlCommandVo
            .setCommandType(RemoteCommandTypeEnum.valueOf(monitorRemoteControlCommandVo.getCommandType()));
    remoteControlCommandVo.setReceiveTimeStamp(new Date());
    remoteControlCommandVo.setTransitTimeStamp(monitorRemoteControlCommandVo.getTimeStamp());
    remoteCommandJsfService.publishRemoteControlCommand(remoteControlCommandVo);
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post reset abnomal control request.
   * </p>
   *
   * @param monitorResetAbnormalControlCommandVO the supervisor remote request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public WsResult postResetAbnormalControlRequest(
          MonitorResetAbnormalControlCommandVO monitorResetAbnormalControlCommandVO, String userName) {
    ParameterCheckUtility.checkNotNull(monitorResetAbnormalControlCommandVO, "monitorResetAbnormalControlCommandVO");
    ParameterCheckUtility.checkNotNullNorEmpty(userName,
            "monitorResetAbnormalControlCommandVO#userName");
    log.info("Receive resetAbnormal command {}.", monitorResetAbnormalControlCommandVO);
    RemoteResetAbnormalCommandVO remoteResetAbnormalCommandVo = new RemoteResetAbnormalCommandVO();
    remoteResetAbnormalCommandVo
            .setCommandType(RemoteCommandTypeEnum.valueOf(monitorResetAbnormalControlCommandVO.getCommandType()));
    remoteResetAbnormalCommandVo.setId(monitorResetAbnormalControlCommandVO.getId());
    remoteResetAbnormalCommandVo.setModuleName(monitorResetAbnormalControlCommandVO.getModuleName());
    remoteResetAbnormalCommandVo.setReceiveTimeStamp(monitorResetAbnormalControlCommandVO.getTimeStamp());
    remoteResetAbnormalCommandVo.setTransitTimeStamp(new Date());
    remoteResetAbnormalCommandVo.setVehicleName(monitorResetAbnormalControlCommandVO.getVehicleName());
    remoteCommandJsfService.publishResetAbnormalCommand(remoteResetAbnormalCommandVo);
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }


  /**
   * <p>
   * Post restart request.
   * </p>
   *
   * @param monitorRestartCommandVo the supervisor restart request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postRestartRequest(MonitorRemoteCommandVO monitorRestartCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorRestartCommandVo, "monitorRestartCommandVo");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorRestartCommandVo.getVehicleName(),
            "monitorRestartCommandVo#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive restart request {}, userName {}.", monitorRestartCommandVo, userName);

    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_RESTART.getValue());

    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(monitorRestartCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(monitorRestartCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishRestartCommand(remoteCommandVo);

    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_RESTART.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());

    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Post as arrived request.
   * </p>
   *
   * @param monitorAsArrivedCommandVo the supervisor as arrived request vo
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postAsArrivedRequest(MonitorRemoteCommandVO monitorAsArrivedCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorAsArrivedCommandVo, "monitorAsArrivedCommandVo");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorAsArrivedCommandVo.getVehicleName(), "monitorAsArrivedCommandVO#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive asArrived request {}, userName {}.", monitorAsArrivedCommandVo, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorAsArrivedCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }

    // 判断车辆PNC是否在任务中，如果当前PNC任务不是ACTIVE状态，则调用调度接口进行业务到达
    String redisKey = RedisKeyEnum.TRAFFIC_LIGHT_SET_VEHICLE.getValue() + monitorAsArrivedCommandVo.getVehicleName();
    SingleVehiclePncTaskAndTrafficLightDTO dto = RedissonUtils.getObject(redisKey);

    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue());
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(monitorAsArrivedCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(monitorAsArrivedCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    // 如果有缓存 并且是ACTIVE状态，则不调用调度接口
    if (Objects.nonNull(dto) && StringUtils.equals("ACTIVE", dto.getPncTaskType())) {
      HttpResult result = remoteCommandJsfService.publishAsArrivedCommand(remoteCommandVo);
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
              WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
      if (!HttpResult.isSuccess(result)) {
        return result;
      }
      return HttpResult.success(remoteCommandDto);
    }else {
      log.info("Receive {} asArrived request vehicleName is {} current pnc status is {} .", userName,monitorAsArrivedCommandVo.getVehicleName(), dto.getPncTaskType());
      HttpResult<Void> result = scheduleMonitorJsfService.vehicleEnd(monitorAsArrivedCommandVo.getVehicleName());
      if (!HttpResult.isSuccess(result)) {
        return result;
      }
      return HttpResult.success(remoteCommandDto);
    }
  }

  /**
   * <p>
   * Post user control request.
   * </p>
   *
   * @return The supervisor remote request response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public WsResult postUserControlRequest(MonitorRemoteControlCommandVO remoteCommandVo, String userName) {
    ParameterCheckUtility.checkNotNull(remoteCommandVo, "userControlCommandVO");
    log.info("Receive user operate request {}.", remoteCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    return WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_USER_CONTROL.getValue(),new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post return command.
   * </p>
   *
   * @param monitorReturnCommandVo the return command vo
   * @return The remote command response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postReturnRequest(MonitorRemoteControlCommandVO monitorReturnCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorReturnCommandVo, "monitorReturnCommandVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(monitorReturnCommandVo.getVehicleName(),
            "monitorReturnCommandVo#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive return request {}, userName {}.", monitorReturnCommandVo, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorReturnCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(monitorReturnCommandVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(monitorReturnCommandVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    //sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo), WebsocketEventTypeEnum..getValue(), userName);
    return HttpResult.success(new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Post pass traffic light command.
   * </p>
   *
   * @param monitorPostTrafficLightCommandVo the pass traffic light command vo
   * @return The supervisor remote command response info includes its result and
   *         message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postPassTrafficLightRequest(
          MonitorPostTrafficLightCommandVO monitorPostTrafficLightCommandVo) {
    ParameterCheckUtility.checkNotNull(monitorPostTrafficLightCommandVo, "monitorPostTrafficLightCommandVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(monitorPostTrafficLightCommandVo.getVehicleName(),
            "monitorPostTrafficLightCommandVo#vehicleName");
    ParameterCheckUtility.checkNotNull(monitorPostTrafficLightCommandVo.getPassThrough(),
            "monitorPostTrafficLightCommandVo#passThrough");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorPostTrafficLightCommandVo.getTrafficLightId(),
            "monitorPostTrafficLightCommandVo#trafficLightId");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive pass traffic light request {}, userName {}.", monitorPostTrafficLightCommandVo, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorPostTrafficLightCommandVo.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_PASS_LIGHT.getValue());
    RemoteTrafficLightCommandVO remoteTrafficLightCommandVo = new RemoteTrafficLightCommandVO();
    remoteTrafficLightCommandVo.setVehicleName(monitorPostTrafficLightCommandVo.getVehicleName());
    remoteTrafficLightCommandVo.setReceiveTimeStamp(monitorPostTrafficLightCommandVo.getTimeStamp());
    remoteTrafficLightCommandVo.setTransitTimeStamp(new Date());
    remoteTrafficLightCommandVo.setPassThrough(monitorPostTrafficLightCommandVo.getPassThrough());
    remoteTrafficLightCommandVo.setTrafficLightId(monitorPostTrafficLightCommandVo.getTrafficLightId());
    HttpResult<Void> result = remoteCommandJsfService.publishControlTrafficLightCommand(remoteTrafficLightCommandVo);
    RemoteCommandVO remoteCommandVo = null;
    if (HttpResult.STATUS_SUCCESS.equals(result.getCode()) && monitorPostTrafficLightCommandVo.getPassThrough()) {
      remoteCommandVo = new RemoteCommandVO();
      remoteCommandVo.setVehicleName(monitorPostTrafficLightCommandVo.getVehicleName());
      remoteCommandVo.setReceiveTimeStamp(monitorPostTrafficLightCommandVo.getTimeStamp());
      remoteCommandVo.setTransitTimeStamp(new Date());
      remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
      vehicleTakeOverRepository.remove(monitorPostTrafficLightCommandVo.getVehicleName());
    }
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteTrafficLightCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_PASS_LIGHT.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (remoteCommandVo != null) {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
              WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    }
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * <p>
   * Post relieve button stop command.
   * </p>
   *
   * @param monitorRemoteCommandVO the relieve button stop command vo
   * @return The remote command response info includes its result and message.
   * @throws IllegalArgumentException If the argument does not meet the
   *                                  requirement.
   */
  public HttpResult postRelieveButtonStopRequest(MonitorRemoteCommandVO monitorRemoteCommandVO) {
    ParameterCheckUtility.checkNotNull(monitorRemoteCommandVO, "monitorRemoteCommandVO");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(monitorRemoteCommandVO.getVehicleName(),
            "monitorRemoteCommandVO#vehicleName");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive relieve button stop request {}, userName {}.", monitorRemoteCommandVO, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(monitorRemoteCommandVO.getVehicleName(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(monitorRemoteCommandVO.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(monitorRemoteCommandVO.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishRelieveButtonStopCommand(remoteCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RELIEVE_BUTTON_STOP.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(new MonitorRemoteCommandDTO());
  }

  /**
   * 发送更改车辆模式请求。
   * @param xataChangeModeCommandVo 包含更改车辆模式所需的信息。
   */
  public HttpResult<MonitorRemoteCommandDTO> postChangeVehicleMode(MonitorXataChangeModeCommandVO xataChangeModeCommandVo) {
    SwitchVehicleModeCommandVO switchVehicleModeCommandVo = new SwitchVehicleModeCommandVO();
    switchVehicleModeCommandVo.setVehicleName(xataChangeModeCommandVo.getVehicleName());
    switchVehicleModeCommandVo.setReceiveTimeStamp(new Date());
    switchVehicleModeCommandVo.setTransitTimeStamp(new Date());
    switchVehicleModeCommandVo.setSwitchMode(xataChangeModeCommandVo.getVehicleMode());
    HttpResult result = remoteCommandJsfService.switchVehicleMode(switchVehicleModeCommandVo);
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(switchVehicleModeCommandVo),
            RemoteOperationTypeEnum.SWITCH_VEHICLE_MODE.getValue(), xataChangeModeCommandVo.getUserName(), RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!HttpResult.isSuccess(result)) {
      return result;
    }
    return HttpResult.success(new MonitorRemoteCommandDTO());
  }

  /**
   * <p>
   * Is able to post.
   * </p>
   *
   * @param vehicleName the vehicle name
   * @param userName    the user name
   * @return ResultInfo
   */
  private MonitorRemoteCommandService.ResultInfo isAbleToPost(String vehicleName, String userName, String commandSource) {
    MonitorRemoteCommandService.ResultInfo resultInfo = new MonitorRemoteCommandService.ResultInfo();
    Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
    if (op.isPresent() && userName.equals(op.get().getUserName()) && StringUtils.equals(commandSource, op.get().getCommandSource())) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(true);
    } else if (op.isPresent() && StringUtils.equals(VehicleRemoteOperationStatusEnum.TEMPORARY.getOperationStatus(), op.get().getOperationStatus())) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(false);
    } else if (!op.isPresent()) {
      resultInfo.setAbleToPost(true);
      resultInfo.setHasRecord(false);
    } else {
      resultInfo.setAbleToPost(false);
      resultInfo.setHasRecord(true);
      resultInfo.setRecordName(op.get().getUserName());
      resultInfo.setCommandSource(op.get().getCommandSource());
    }
    return resultInfo;
  }

  /**
   * <p>
   * Send record log to kafka.
   * </p>
   *
   * @return ResultInfo
   */
  public void sendRemoteCommandRecordLog(String data,String eventType, String userName, String source) {
    MonitorUserOperationEntity monitorUserOperationLog = new MonitorUserOperationEntity();
    monitorUserOperationLog.setUserName(userName);
    monitorUserOperationLog.setOperationType(eventType);
    monitorUserOperationLog.setTimestamp(new Date());
    monitorUserOperationLog.setOperationSource(source);
    if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage("Takeover: enable emergency stop");
    } if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_TEMPORARY_STOP.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage("临时接管车辆");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage("Takeover: recovery. Disable takeover.");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue())) {
      TypeReference<MonitorRemoteControlCommandVO> typeReference = new TypeReference<MonitorRemoteControlCommandVO>() {
      };
      MonitorRemoteControlCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage( "Takeover: enable remote control with " + commandVo.toString());
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_BRAKE.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage("车辆遥控中操作急刹");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_RESTART.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage( "One-time Command: Restart.");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage( "One-time Command: As Arrived.");
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_PASS_LIGHT.getValue())) {
      TypeReference<RemoteTrafficLightCommandVO> typeReference = new TypeReference<RemoteTrafficLightCommandVO>() {
      };
      RemoteTrafficLightCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      StringBuilder strBuilder = new StringBuilder();
      strBuilder.append("One-time Command: ");
      strBuilder.append(commandVo.getPassThrough() ? "Pass Traffic Light " : "Cancel pass Traffic Light ");
      strBuilder.append(Arrays.toString(commandVo.getTrafficLightId().toArray()));
      monitorUserOperationLog.setOperationMessage(strBuilder.toString());
    } else if (StringUtils.equals(eventType,
            WebsocketEventTypeEnum.REMOTE_REQUEST_RELIEVE_BUTTON_STOP.getValue())) {
      TypeReference<RemoteCommandVO> typeReference = new TypeReference<RemoteCommandVO>() {
      };
      RemoteCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage( "One-time Command: Relieve Button Stop.");
    } else if (StringUtils.equals(eventType, WebsocketEventTypeEnum.REMOTE_NO_SIGNAL_INTERSECTION.getValue())) {
      PassNoSignalIntersectionCommandVO commandVo = JsonUtils.readValue(data, PassNoSignalIntersectionCommandVO.class);
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      String msg = "One-time Command: Pass No Signal Intersection " + Arrays.toString(commandVo.getGroupLaneIdList().toArray());
      monitorUserOperationLog.setOperationMessage(msg);
    } else if (StringUtils.equals(eventType, WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue())) {
      S3PreSignVO commandVo = JsonUtils.readValue(data, S3PreSignVO.class);
      if (Objects.isNull(commandVo.getVehicleName()) || Objects.equals(commandVo.getVehicleName(), commandVo.getUserName())) {
        monitorUserOperationLog.setVehicleName(commandVo.getUserName());
        String operationMessage = String.format("车辆[%s]请求发送文件[%s]至服务端", commandVo.getUserName(), commandVo.getFileName());
        monitorUserOperationLog.setOperationMessage(operationMessage);
      } else {
        monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
        String operationMessage = String.format("用户[%s]请求从服务端发送文件[%s]至车辆[%s]", commandVo.getUserName()
                , commandVo.getFileName(), commandVo.getVehicleName());
        monitorUserOperationLog.setOperationMessage(operationMessage);
      }
    } else if (StringUtils.equals(eventType, RemoteOperationTypeEnum.SWITCH_VEHICLE_MODE.getValue())) {
      TypeReference<SwitchVehicleModeCommandVO> typeReference = new TypeReference<SwitchVehicleModeCommandVO>() {
      };
      SwitchVehicleModeCommandVO commandVo = JsonUtils.readValue(data, typeReference);
      String operationMessage = String.format("用户[%s]切换车辆[%s]模式为%s", userName,commandVo.getVehicleName(), commandVo.getSwitchMode());
      monitorUserOperationLog.setVehicleName(commandVo.getVehicleName());
      monitorUserOperationLog.setOperationMessage(operationMessage);
    }
    String kafkaData = JsonUtils.writeValueAsString(monitorUserOperationLog);
    kafkaTemplate.send(MonitorKafkaTopicConstant.MONITER_REMOTE_COMMAND_LOG, kafkaData)
            .whenComplete((result, ex) -> {
              if (Objects.isNull(ex)) {
                log.info("Send {} remote command record log {} success", MonitorKafkaTopicConstant.MONITER_REMOTE_COMMAND_LOG, kafkaData);
              } else {
                log.error("Send remote command record log fail {}", ex.getStackTrace());
              }
            });
    jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_REMOTE_COMMAND_LOG.getTopic(), monitorUserOperationLog.getVehicleName(), monitorUserOperationLog);
  }

  /**
   * 电源管理
   *
   * @param powerManagerAction
   * @param vehicleName
   * @return
   */
  public HttpResult powerManager(String powerManagerAction, String vehicleName, String userName) {
    HttpResult httpResult = powerManagerManager.powerManager(powerManagerAction, vehicleName, userName);
    PowerManagerCommandVO powerManagerCommandVO = powerManagerManager.initPowerManagerCommandVO(powerManagerAction, vehicleName);
    eventCollectService.pushUserEvent(powerManagerManager.initMonitorUserOperationLog(powerManagerCommandVO, userName));
    // 发送mq消息
    if (PowerManagerActionEnum.POWER_OFF.getValue().equals(powerManagerAction)) {
      try {
        UserOperateCommandDTO commandDTO = UserOperateCommandDTO.builder()
                .operateUser(userName)
                .vehicleName(vehicleName)
                .operateTime(new Date())
                .source(RemoteCommandSourceEnum.MONITOR.getCommandSource())
                .operateType(H5RemotePowerEnum.POWER_OFF.getValue())
                .operateResult(HttpResult.isSuccess(httpResult) ? 1 : 0)
                .build();
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_H5_TRANSPORT_COMMAND.getTopic(), commandDTO.getVehicleName(), commandDTO);
      } catch (Exception e) {
        log.error("发送mq消息失败", e);
      }
    }
    return httpResult;
  }

  /**
   * 通过无信号路口
   */
  public HttpResult passNoSignalIntersection(
          NoSignalIntersectionCommandVO passNoSignal) {
    ParameterCheckUtility.checkNotNull(passNoSignal, "PassNoSignalIntersectionCommandVO");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(passNoSignal.getVehicleName(),
            "PassNoSignalIntersectionCommandVO#vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(passNoSignal.getGroupLaneIdList(),
            "PassNoSignalIntersectionCommandVO#getGroupLaneIdList");
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive pass no signal intersection request {}, userName {}.", passNoSignal, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(passNoSignal.getVehicleName(), userName
            , RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("The vehicle is being taken over by %s.", resultInfo.getRecordName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    PassNoSignalIntersectionCommandVO commandVO = new PassNoSignalIntersectionCommandVO();
    commandVO.setVehicleName(passNoSignal.getVehicleName());
    commandVO.setReceiveTimeStamp(passNoSignal.getTimeStamp());
    commandVO.setTransitTimeStamp(new Date());
    commandVO.setGroupLaneIdList(passNoSignal.getGroupLaneIdList());
    HttpResult<Void> result = remoteCommandJsfService.passNoSignalIntersection(commandVO);
    log.info("Pass no signal intersection command result={}.", result);

    RemoteCommandVO remoteCommandVo = null;
    if (HttpResult.STATUS_SUCCESS.equals(result.getCode())) {
      remoteCommandVo = new RemoteCommandVO();
      remoteCommandVo.setVehicleName(passNoSignal.getVehicleName());
      remoteCommandVo.setReceiveTimeStamp(passNoSignal.getTimeStamp());
      remoteCommandVo.setTransitTimeStamp(new Date());
      remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
      vehicleTakeOverRepository.remove(passNoSignal.getVehicleName());
    }
    sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(commandVO),
            WebsocketEventTypeEnum.REMOTE_NO_SIGNAL_INTERSECTION.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    if (remoteCommandVo != null) {
      sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
              WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), userName, RemoteCommandSourceEnum.MONITOR.getCommandSource());
    }

    if (!HttpResult.STATUS_SUCCESS.equals(result.getCode())) {
      return result;
    }

    MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
    remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_NO_SIGNAL_INTERSECTION.getValue());
    return HttpResult.success(remoteCommandDto);
  }

  /**
   * 临时停车
   */
  public HttpResult postTemporaryStopCommand(MonitorRemoteCommandVO temporaryStopVo) {
    String userName = UserUtils.getAndCheckLoginUser();
    log.info("Receive temporary stop request {}, userName {}.", temporaryStopVo, userName);
    MonitorRemoteCommandService.ResultInfo resultInfo = isAbleToPost(temporaryStopVo.getVehicleName(), userName, temporaryStopVo.getCommandSource());
    if (!resultInfo.getAbleToPost()) {
      String message = String.format("车辆正在被用户%s在%s端接管中.", resultInfo.getRecordName(), RemoteCommandSourceEnum.of(resultInfo.getCommandSource()).getCommandSourceName());
      return HttpResult.error(MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getCode(), message);
    }
    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
    remoteCommandVo.setVehicleName(temporaryStopVo.getVehicleName());
    remoteCommandVo.setReceiveTimeStamp(temporaryStopVo.getTimeStamp());
    remoteCommandVo.setTransitTimeStamp(new Date());
    HttpResult result = remoteCommandJsfService.publishEmergencyStopCommand(remoteCommandVo);
    if (!HttpResult.isSuccess(result)) {
      log.info("临时接管失败!{},{}", result, temporaryStopVo);
      return result;
    }
    UserStatusDO userStatusDo = userStatusRepository.get(userName);
    VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
    vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TEMPORARY.getOperationStatus());
    vehicleTakeOverEntity.setUserName(userName);
    if (!Objects.isNull(userStatusDo)) {
      vehicleTakeOverEntity.setCockpitNumber(userStatusDo.getCockpitNumber());
    }
    vehicleTakeOverEntity.setOperateTime(new Date());
    vehicleTakeOverEntity.setCommandSource(temporaryStopVo.getCommandSource());
    vehicleTakeOverRepository.save(temporaryStopVo.getVehicleName(), vehicleTakeOverEntity);
    try {
      if (!resultInfo.getHasRecord()) {
        sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(temporaryStopVo),
                WebsocketEventTypeEnum.REMOTE_REQUEST_TEMPORARY_STOP.getValue(), userName, temporaryStopVo.getCommandSource());
      }
    } catch (Exception e) {
      log.error("Store user remote command record exception", e);
    }
    return HttpResult.success();
  }

  /**
   * 获取用户接管的车辆列表
   */
  public HttpResult<List<VehicleTakeOverDTO>> getTakeOverVehicle(String userName) {
    log.info("Receive get takeOver vehicle request {}.", userName);
    Set<String> userVehicleList = userVehicleNameRepository.get(userName);
    if (CollectionUtils.isEmpty(userVehicleList)) {
      return HttpResult.success();
    }
    List<VehicleTakeOverEntity> takeOverList = vehicleTakeOverRepository.getTakeOverVehicle(userName, Lists.newArrayList(userVehicleList));
    List<VehicleTakeOverDTO> takeOverDtoList = takeOverList.stream().map(entity -> {
      VehicleTakeOverDTO takeOverDto = new VehicleTakeOverDTO();
      takeOverDto.setCommandSource(entity.getCommandSource());
      takeOverDto.setUserName(entity.getUserName());
      takeOverDto.setOperateTime(entity.getOperateTime());
      takeOverDto.setOperationStatus(entity.getOperationStatus());
      return takeOverDto;
    }).collect(Collectors.toList());
    return HttpResult.success(takeOverDtoList);
  }

  /**
   * 远驾远程开机
   */
  public MonitorErrorEnum powerOn(MonitorVehiclePowerOnVO monitorVehiclePowerOnVO) {
    String username = JsfLoginUtil.getUsername();
    Date operateTime = new Date();
    MonitorErrorEnum monitorErrorEnum = commandManager.sendPdu(monitorVehiclePowerOnVO.getVehicleName(), CommandTypeEnum.REMOTE_POWER_ON);
    // 发送mq消息
    try {
      UserOperateCommandDTO commandDTO = UserOperateCommandDTO.builder()
              .operateUser(username)
              .vehicleName(monitorVehiclePowerOnVO.getVehicleName())
              .operateTime(operateTime)
              .source(RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource())
              .operateType(H5RemotePowerEnum.POWER_ON.getValue())
              .operateResult(MonitorErrorEnum.OK.equals(monitorErrorEnum) ? 1 : 0)
              .build();
      jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_H5_TRANSPORT_COMMAND.getTopic(), commandDTO.getVehicleName(), commandDTO);
    } catch (Exception e) {
      log.error("发送mq消息失败", e);
    }
    return monitorErrorEnum;
  }

  /**
   * 控制LocalView链路状态。
   * @param localViewCtrlVO
   */
  public void localViewCtrl(LocalViewCtrlVO localViewCtrlVO){

    String localViewCtrlType = localViewCtrlVO.getLocalViewCtrlType();
    String vehicleName = localViewCtrlVO.getVehicleName();
    String userName = UserUtils.getLoginUser();
    log.info("【LocalView链路控制】：[vehicle：{} , isLocalViewOpen：{} , userName：{} ]", vehicleName, localViewCtrlType, userName);
    LocalViewCtrlCommandVO localViewCtrlCommandVO = new LocalViewCtrlCommandVO();
    localViewCtrlCommandVO.setVehicleName(vehicleName);
    localViewCtrlCommandVO.setReceiveTimeStamp(new Date());
    localViewCtrlCommandVO.setTransitTimeStamp(new Date());
    localViewCtrlCommandVO.setLocalViewCtrlType(localViewCtrlVO.getLocalViewCtrlType());
    try{
      HttpResult<Void> result = remoteCommandJsfService.localViewCtrl(localViewCtrlCommandVO);
      log.info("【LocalView链路控制】下发指令结果 :[{}]" , result);
      if (!HttpResult.isSuccess(result)) {
        throw new RuntimeException(result.getMessage());
      }
    }catch (Exception e){
      log.error("【LocalView链路控制】下发指令失败", e);
      throw new AppException(e.getMessage());
    }
  }
}