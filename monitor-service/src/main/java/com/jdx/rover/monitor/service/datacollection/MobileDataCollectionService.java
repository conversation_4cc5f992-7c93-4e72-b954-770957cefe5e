/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.datacollection;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementDetailDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementListDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementTagsListDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollTaskGetPlanRouteDTO;
import com.jdx.rover.monitor.entity.datacollection.DataCollectionPlanRoutePointDO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionTagTypeEnum;
import com.jdx.rover.monitor.jsf.BusinessCheckUtil;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionRequirementManager;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionRequirementTagManager;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionSceneManager;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionTaskManager;
import com.jdx.rover.monitor.manager.notice.JdMeNoticeManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionRequirement;
import com.jdx.rover.monitor.po.datacollection.DataCollectionRequirementTag;
import com.jdx.rover.monitor.po.datacollection.DataCollectionScene;
import com.jdx.rover.monitor.po.datacollection.DataCollectionTask;
import com.jdx.rover.monitor.repository.redis.datacollection.DataCollPlanRouteRepository;
import com.jdx.rover.monitor.repository.redis.datacollection.DataCollectionVehicleUserRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import com.jdx.rover.monitor.vo.mobile.datacollection.*;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序数据采集服务
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MobileDataCollectionService {

    private final DataCollectionRequirementManager dataCollectionRequirementManager;

    private final DataCollectionRequirementTagManager dataCollectionRequirementTagManager;

    private final DataCollectionSceneManager dataCollectionSceneManager;

    private final ThreadPoolTaskExecutor dataCollectionSceneTagThreadPool;

    private final DataCollectionSceneService dataCollectionSceneService;

    private final DataCollectionService dataCollectionService;

    private final JdMeNoticeManager jdMeNoticeManager;

    private final DataCollPlanRouteRepository dataCollPlanRouteRepository;

    private final DataCollectionTaskManager dataCollectionTaskManager;

    private final DataCollectionVehicleUserRepository dataCollectionVehicleUserRepository;

    private final MetadataUserApiManager metadataUserApiManager;

    // ducc配置
    private final DuccMobileProperties duccMobileProperties;

    // 咚咚通知人
    private final String ddNoticeErps = "linbingbing6,cuihongjian1,gulin21";

    /**
     * 获取进行中的采集需求列表
     * @return 采集需求列表
     */
    public List<MobileDataCollRequirementListDTO> getRequirementList() {
        // ONGOING的需求卡片才会是进度小于100%，因此用状态过滤即可
        List<DataCollectionRequirement> requirementList = dataCollectionRequirementManager
            .listByStatus(DataCollectionStatusEnum.ONGOING.getValue());
        if(CollectionUtil.isEmpty(requirementList)) {
            return Collections.emptyList();
        }
        List<Integer> requirementIdList = requirementList.stream()
            .map(DataCollectionRequirement::getId).collect(Collectors.toList());
        List<DataCollectionRequirementTag> tagList = dataCollectionRequirementTagManager
            .listByRequirementIdCollAndEnable(requirementIdList, EnableEnum.ENABLE.getValue());
         Map<Integer, List<DataCollectionRequirementTag>> requirementTagList = tagList.stream()
             .collect(Collectors.groupingBy(DataCollectionRequirementTag::getRequirementId));

        List<MobileDataCollRequirementListDTO> dtoList = new ArrayList<>(requirementList.size());
        requirementList.forEach(requirement ->
            dtoList.add(MobileDataCollRequirementListDTO.builder()
                    .requirementId(requirement.getId())
                    .description(requirement.getDescription())
                    .forbiddenDetail(requirement.getForbiddenDetail())
                    .requiredDetail(requirement.getRequiredDetail())
                    .progress(dataCollectionService.formatProgress(requirement, requirementTagList.get(requirement.getId())))
                    .build()));

        // 按照 progress 字段从小到大排序，progress 为 null 的记录排在最后面
        return dtoList.stream()
                .sorted(Comparator.comparing(MobileDataCollRequirementListDTO::getProgress,
                        Comparator.nullsLast((Double::compareTo))))
                .collect(Collectors.toList());
    }

    /**
     * 获取采集需求详情
     * @param mobileDataCollRequirementDetailVO 采集需求详情入参
     * @return 采集需求详情
     */
    public MobileDataCollRequirementDetailDTO getRequirementDetail(MobileDataCollRequirementDetailVO mobileDataCollRequirementDetailVO) {
        DataCollectionRequirement dataCollectionRequirement = dataCollectionRequirementManager.getById(mobileDataCollRequirementDetailVO.getRequirementId());
        BusinessCheckUtil.checkNull(dataCollectionRequirement, MonitorErrorEnum.ERROR_COLLECTION_REQUIREMENT_NOT_EXIST);
        List<DataCollectionRequirementTag> enabledCollectionRequirementTagList = dataCollectionRequirementTagManager
            .listByRequirementId(dataCollectionRequirement.getId(), EnableEnum.ENABLE.getValue());
        List<MobileDataCollRequirementDetailDTO.TagDTO> tagList = new ArrayList<>(enabledCollectionRequirementTagList.size());
        enabledCollectionRequirementTagList.forEach(tag ->
            tagList.add(MobileDataCollRequirementDetailDTO.TagDTO.builder()
                .tagId(tag.getTagId())
                .parentId(tag.getParentTagId())
                .tagName(tag.getTagName())
                .tagType(tag.getTagType())
                .build()));
        return MobileDataCollRequirementDetailDTO.builder()
            .description(dataCollectionRequirement.getDescription())
            .requirementNumber(dataCollectionRequirement.getRequirementNumber())
            .progress(dataCollectionService.formatProgress(dataCollectionRequirement, enabledCollectionRequirementTagList))
            .createTime(dataCollectionRequirement.getCreateTime())
            .createUser(dataCollectionRequirement.getCreateUser())
            .requiredDetail(dataCollectionRequirement.getRequiredDetail())
            .forbiddenDetail(dataCollectionRequirement.getForbiddenDetail())
            .tagList(tagList)
            .build();
    }

    /**
     * 获取全量进行中的需求标签
     * @return 全量进行中的需求标签
     */
    public MobileDataCollRequirementTagsListDTO getRequirementTagsList() {
        List<DataCollectionRequirement> requirementList = dataCollectionRequirementManager
            .listByStatus(DataCollectionStatusEnum.ONGOING.getValue());
        if(CollectionUtil.isEmpty(requirementList)) {
            return new MobileDataCollRequirementTagsListDTO();
        }
        List<DataCollectionRequirementTag> tagList = dataCollectionRequirementTagManager
            .listByRequirementIdCollAndEnable(requirementList.stream().map(DataCollectionRequirement::getId)
                    .collect(Collectors.toList()),
                EnableEnum.ENABLE.getValue());
        if(CollectionUtil.isEmpty(tagList)) {
            return new MobileDataCollRequirementTagsListDTO();
        }
        Map<String, Set<String>> requirementTagMap = tagList.stream()
            .collect(Collectors.groupingBy(
                DataCollectionRequirementTag::getTagType,
                Collectors.mapping(DataCollectionRequirementTag::getTagName, Collectors.toSet())
            ));
        return MobileDataCollRequirementTagsListDTO.builder()
            .mainTagNameList(requirementTagMap.get(DataCollectionTagTypeEnum.MAIN.getValue()))
            .childrenTagNameList(requirementTagMap.get(DataCollectionTagTypeEnum.CHILDREN.getValue()))
            .detailTagNameList(requirementTagMap.get(DataCollectionTagTypeEnum.DETAIL.getValue()))
            .build();
    }

    /**
     * 关联子任务语音信息
     * @param mobileDataCollTaskSceneAudioAssociateVO 关联子任务语音信息入参
     */
    public void associateTaskSceneAudio(MobileDataCollTaskSceneAudioAssociateVO mobileDataCollTaskSceneAudioAssociateVO) {
        Integer sceneId = mobileDataCollTaskSceneAudioAssociateVO.getSceneId();
        String content = mobileDataCollTaskSceneAudioAssociateVO.getSpeechRecognitionResult();
        String vehicleName = mobileDataCollTaskSceneAudioAssociateVO.getVehicleName();
        String fileKey = mobileDataCollTaskSceneAudioAssociateVO.getAudioFileKey();

        if(StrUtil.isAllBlank(fileKey, content)) {
            throw new AppException("语音文件key和语音识别结果不能同时为空");
        }
        DataCollectionScene dataCollectionScene = dataCollectionSceneManager.getByIdVehicleNameAndCheck(sceneId,vehicleName);
        dataCollectionSceneManager.updateAudio(dataCollectionScene.getId(), fileKey, content);
        if(StrUtil.isNotBlank(content)) {
            // 异步关联标签
            try {
                dataCollectionSceneService.associateSceneTag(sceneId, content);
            } catch (Exception e) {
                log.error("[数采车]车辆{}场景[{}]语音[{}]关联标签失败", vehicleName, sceneId, content, e);
                String msg = String.format("[数采车]车辆%s场景[%s]语音关联标签失败", vehicleName, sceneId);
                jdMeNoticeManager.sendNotice("数采车场景语音关联标签失败", msg, ddNoticeErps);
            }
        }
    }

    /**
     * 保存规划路径
     * @param mobileDataCollTaskSavePlanRouteVO 保存规划路径入参
     */
    public void saveTaskPlanRoute(MobileDataCollTaskSavePlanRouteVO mobileDataCollTaskSavePlanRouteVO) {
        List<DataCollectionPlanRoutePointDO> routeDOList = mobileDataCollTaskSavePlanRouteVO.getPointList().stream().map(point ->
            new DataCollectionPlanRoutePointDO(point.getLatitude(), point.getLongitude())).collect(Collectors.toList());
        dataCollPlanRouteRepository.save(mobileDataCollTaskSavePlanRouteVO.getVehicleName(), mobileDataCollTaskSavePlanRouteVO.getClientId(),
            routeDOList);
    }

    /**
     * 获取规划路径
     * @param mobileDataCollTaskGetPlanRouteVO 获取规划路径入参
     * @return 规划路径
     */
    public List<MobileDataCollTaskGetPlanRouteDTO> getTaskPlanRoute(MobileDataCollTaskGetPlanRouteVO mobileDataCollTaskGetPlanRouteVO) {
        // https://github.com/FasterXML/jackson-databind/issues/3404
        // 不能用.toList();返回Collections.unmodifiableList,redis中直接写["a","b"],在redis里面不会写类型["java.util.ArrayList",["a","b"]],否则jackson不能反序列化
        List<DataCollectionPlanRoutePointDO> routeDOList = dataCollPlanRouteRepository.get(mobileDataCollTaskGetPlanRouteVO.getVehicleName(),
            mobileDataCollTaskGetPlanRouteVO.getClientId());
        if (CollectionUtil.isEmpty(routeDOList)) {
            return Collections.emptyList();
        }
        return routeDOList.stream().map(routeDO -> new MobileDataCollTaskGetPlanRouteDTO(routeDO.getLatitude(), routeDO.getLongitude()))
            .collect(Collectors.toList());
    }

    /**
     * 删除规划路径
     * @param mobileDataCollTaskRemovePlanRouteVO 删除规划路径入参
     */
    public void removeTaskPlanRoute(MobileDataCollTaskRemovePlanRouteVO mobileDataCollTaskRemovePlanRouteVO) {
        dataCollPlanRouteRepository.remove(mobileDataCollTaskRemovePlanRouteVO.getVehicleName(),
            mobileDataCollTaskRemovePlanRouteVO.getClientId());
    }

    /**
     * 判断是否能进入采集模式
     * @param mobileDataCollCanEnterCollectionModeVO 判断是否能进入采集模式入参
     */
    public void canEnterCollectionMode(MobileDataCollCanEnterCollectionModeVO mobileDataCollCanEnterCollectionModeVO) {
        String vehicleName = mobileDataCollCanEnterCollectionModeVO.getVehicleName();
        // 判断车辆是否是数采车
        if (!duccMobileProperties.getDataCollVehicleNameList().contains(vehicleName)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_NOT_EXIST.getCode(),
                MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_NOT_EXIST.getMessage());
        }
        DataCollectionTask task = dataCollectionTaskManager.getUnfinishedTaskByVehicleName(vehicleName);
        if (Objects.isNull(task)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_TASK_NOT_EXIST.getCode(),
                MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_TASK_NOT_EXIST.getMessage());
        }
        String username = UserUtils.getAndCheckLoginUser();
        String oldUsername = dataCollectionVehicleUserRepository.get(vehicleName);
        if (Objects.nonNull(oldUsername) && !StrUtil.equals(username, oldUsername)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_HAS_USER.getCode(),
                String.format(MonitorErrorEnum.ERROR_COLLECTION_VEHICLE_HAS_USER.getMessage(), oldUsername));
        }
    }

    /**
     * 更新任务采集人信息
     * @param username 用户名
     * @param task 采集任务
     */
    public void updateTaskCreateUser(String username, DataCollectionTask task) {
        try {
            UserExtendInfoDTO userExtendInfoDTO = metadataUserApiManager.getUserExtendInfoByName(username);
            if (Objects.isNull(userExtendInfoDTO) || StrUtil.isBlank(userExtendInfoDTO.getJdErp())) {
                log.info("[数采车]根据用户名{}获取用户erp失败, 不更新采集任务创建人", username);
                return;
            }
            boolean hasCreateUser = StrUtil.isNotBlank(task.getCreateUser());
            if (hasCreateUser) {
                String[] createUsers = task.getCreateUser().split(StrPool.COMMA);
                if (StrUtil.equals(username, createUsers[createUsers.length - 1])) {
                    log.info("[数采车]当前用户{}为车辆{}采集任务最后一个创建人，不更新", username, task.getVehicleName());
                    return;
                }
            }
            String createUser = hasCreateUser ? StrUtil.join(StrPool.COMMA, task.getCreateUser(), username) : username;
            dataCollectionTaskManager.updateCreateUser(task.getId(), createUser);
        } catch (Exception e) {
            log.error("[数采车]{}更新采集任务创建人失败", task.getVehicleName(), e);
        }
    }
}