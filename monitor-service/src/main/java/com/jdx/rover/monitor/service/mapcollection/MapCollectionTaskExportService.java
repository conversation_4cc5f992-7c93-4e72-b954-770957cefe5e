/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.service.mapcollection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.HttpMethod;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.enums.RoverBucketEnum;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.api.domain.enums.common.EnableEnum;
import com.jdx.rover.metadata.api.domain.enums.common.OperationTypeEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.dto.mapcollection.MapCollectionTaskExportResultDTO;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.stop.MetadataStopApiManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.po.mapcollection.json.TaskRoutePoint;
import com.jdx.rover.monitor.repository.s3.S3Properties;
import com.jdx.rover.monitor.repository.s3.S3Utils;
import com.jdx.rover.monitor.vo.deployment.DeploySyncTaskVO;
import com.jdx.rover.monitor.vo.mapcollection.MapCollectionTaskExportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 勘查任务导出服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MapCollectionTaskExportService {

    /**
     * MapCollectionTaskManager
     */
    private final MapCollectionTaskManager mapCollectionTaskManager;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * MetadataStopApiManager
     */
    private final MetadataStopApiManager metadataStopApiManager;

    private final DeploymentMapTaskService deploymentMapTaskService;

    /**
     * S3Properties
     */
    private final S3Properties s3Properties;

    /**
     * GeometryFactory，用于创建几何对象
     */
    private final GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

    /**
     * 导出勘查任务数据并上传至S3对象存储（TXT格式）
     *
     * @param exportVO 导出参数
     * @return 导出结果，包含下载链接
     */
    public MapCollectionTaskExportResultDTO exportMapCollectionTaskDataToS3(MapCollectionTaskExportVO exportVO) {
        try {
            // 查询勘查任务数据
            List<MapCollectionTask> taskList = queryMapCollectionTasks(exportVO);
            if (CollUtil.isEmpty(taskList)) {
                return new MapCollectionTaskExportResultDTO("无数据", "", 0);
            }

            // 生成GeoJSON文件并上传到S3
            return generateBatchGeoJsonAndUploadToS3(taskList);
        } catch (Exception e) {
            log.error("导出勘查任务数据并上传至S3失败", e);
            return new MapCollectionTaskExportResultDTO("导出失败", "", 0);
        }
    }

    /**
     * 将勘查任务数据导出到部署平台。
     * @param deploySyncTaskVo 部署同步任务对象，包含要导出的勘查任务列表。
     */
    public void exportMapCollectionTaskDataToDeployment(DeploySyncTaskVO deploySyncTaskVo) {
        try {
            // 查询勘查任务数据
            deploySyncTaskVo.getStationTaskList().stream().forEach(task -> {
                MapCollectionTaskExportVO exportVO = new MapCollectionTaskExportVO();
                exportVO.setStationId(Lists.newArrayList(task.getStationId()));
                List<MapCollectionTask> taskList = queryMapCollectionTasks(exportVO);
                if (CollUtil.isEmpty(taskList)) {
                    return;
                }
                String supplier = StringUtils.isBlank(task.getSupplier())? SupplierEnum.JD.getValue() : task.getSupplier();
                String operation = StringUtils.isBlank(task.getOperation())? OperationTypeEnum.ADD.getValue() : task.getOperation();
                taskList.stream().forEach(mapTask -> deploymentMapTaskService.syncCollectionTaskToDeploymentMap(mapTask, task.getRequireNumber(), supplier, operation));
            });
        } catch (Exception e) {
            log.error("导出任务数据到部署平台失败", e);
        }

    }

    /**
     * 将TaskRoutePoint列表转换为GeoJSON LineString格式，返回Map对象
     *
     * @param task 勘查任务
     * @param stationName 站点名称
     * @return GeoJSON Feature Map对象
     */
    private Map<String, Object> convertTaskRouteToGeoJsonMap(MapCollectionTask task, String stationName) throws Exception {
        List<TaskRoutePoint> routePoints = task.getTaskRoute();
        if (CollUtil.isEmpty(routePoints)) {
            return null;
        }

        // 创建Coordinates数组
        Coordinate[] coordinates = new Coordinate[routePoints.size()];
        for (int i = 0; i < routePoints.size(); i++) {
            TaskRoutePoint point = routePoints.get(i);
            coordinates[i] = new Coordinate(point.getLongitude(), point.getLatitude());
        }

        // 创建LineString
        LineString lineString = geometryFactory.createLineString(coordinates);

        // 添加属性信息
        Map<String, Object> properties = new HashMap<>();
        properties.put("taskId", task.getId());
        properties.put("taskName", task.getTaskName());
        properties.put("totalMileage", task.getTotalMileage());
        properties.put("featureType", "route"); // 标记为路线类型

        // 添加站点名称
        if (StrUtil.isNotBlank(stationName)) {
            properties.put("stationName", stationName);
        }

        // 使用GeoTools的GeometryJSON将LineString转换为GeoJSON
        StringWriter writer = new StringWriter();
        GeometryJSON geometryJSON = new GeometryJSON(8); // 8位小数精度
        geometryJSON.write(lineString, writer);

        // 创建GeoJSON Feature
        Map<String, Object> feature = new LinkedHashMap<>();
        feature.put("type", "Feature");
        feature.put("properties", properties);
        feature.put("geometry", JsonUtils.readValue(writer.toString(), Map.class));

        return feature;
    }

    /**
     * 将停靠点转换为GeoJSON Point格式
     *
     * @param stop 停靠点
     * @param stationName 站点名称
     * @return GeoJSON Feature Map对象
     */
    private Map<String, Object> convertStopToGeoJsonMap(StopBasicDTO stop, String stationName) throws Exception {
        if (stop == null || stop.getLatitude() == null || stop.getLongitude() == null) {
            return null;
        }

        // 创建Point
        Point point = geometryFactory.createPoint(new Coordinate(stop.getLongitude(), stop.getLatitude()));

        // 添加属性信息
        Map<String, Object> properties = new HashMap<>();
        properties.put("stopId", stop.getStopId());
        properties.put("stopName", stop.getStopName());
        properties.put("featureType", "stop"); // 标记为停靠点类型

        // 添加站点名称
        if (StrUtil.isNotBlank(stationName)) {
            properties.put("stationName", stationName);
        }

        // 使用GeoTools的GeometryJSON将Point转换为GeoJSON
        StringWriter writer = new StringWriter();
        GeometryJSON geometryJSON = new GeometryJSON(8); // 8位小数精度
        geometryJSON.write(point, writer);

        // 创建GeoJSON Feature
        Map<String, Object> feature = new LinkedHashMap<>();
        feature.put("type", "Feature");
        feature.put("properties", properties);
        feature.put("geometry", JsonUtils.readValue(writer.toString(), Map.class));

        return feature;
    }

    /**
     * 生成批量GeoJSON文件并上传到S3
     *
     * @param taskList 勘查任务列表
     * @return 导出结果，包含下载链接
     */
    private MapCollectionTaskExportResultDTO generateBatchGeoJsonAndUploadToS3(List<MapCollectionTask> taskList) throws Exception {
        // 创建GeoJSON FeatureCollection
        Map<String, Object> featureCollection = new LinkedHashMap<>();
        featureCollection.put("type", "FeatureCollection");

        // 收集所有任务的Feature
        List<Map<String, Object>> features = new ArrayList<>();
        int recordCount = 0;

        // 收集所有站点ID
        Set<Integer> stationIds = taskList.stream()
            .map(MapCollectionTask::getStationId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 获取站点信息
        Map<Integer, StationBasicDTO> stationMap = metadataStationApiManager.getStationById(stationIds.toArray(new Integer[0]));

        // 获取所有站点下的停靠点
        Map<Integer, List<StopBasicDTO>> stationStopsMap = new HashMap<>();
        for (Integer stationId : stationIds) {
            List<StopBasicDTO> stops = metadataStopApiManager.getStopByStationId(stationId);
            if (CollUtil.isNotEmpty(stops)) {
                stationStopsMap.put(stationId, stops);
            }
        }

        Set<Integer> stationStopFlag = new HashSet<>();
        for (MapCollectionTask task : taskList) {
            // 处理任务路线
            if (CollUtil.isNotEmpty(task.getTaskRoute())) {
                recordCount++;

                // 获取站点信息
                StationBasicDTO stationBasicDTO = task.getStationId() != null ? stationMap.get(task.getStationId()) : null;
                String stationName = stationBasicDTO != null ? stationBasicDTO.getStationName() : "";

                // 转换任务路线为GeoJSON
                Map<String, Object> routeFeature = convertTaskRouteToGeoJsonMap(task, stationName);
                if (routeFeature != null) {
                    features.add(routeFeature);
                }

                // 添加站点下的停靠点
                if (task.getStationId() != null && !stationStopFlag.contains(task.getStationId())) {
                    List<StopBasicDTO> stops = stationStopsMap.get(task.getStationId());
                    if (CollUtil.isNotEmpty(stops)) {
                        for (StopBasicDTO stop : stops) {
                            // 仅保留启用停靠点
                            if (!Objects.equals(stop.getEnable(), EnableEnum.ENABLE.getEnable())) {
                                continue;
                            }
                            Map<String, Object> stopFeature = convertStopToGeoJsonMap(stop, stationName);
                            if (stopFeature != null) {
                                features.add(stopFeature);
                            }
                        }
                    }
                    stationStopFlag.add(task.getStationId());
                }
            }
        }

        featureCollection.put("features", features);

        // 转换为JSON字符串
        String geoJson = JsonUtils.writeValueAsString(featureCollection);
        log.info("线路绘制路线导出：[{}]", geoJson);

        // 上传到S3
        ByteArrayInputStream inputStream = null;
        try {
            // 构建S3文件路径
            String fileKey = "线路绘制路线-" + System.currentTimeMillis() +  ".geojson";

            // 上传文件到S3
            byte[] geoJsonBytes = geoJson.getBytes(StandardCharsets.UTF_8);
            inputStream = new ByteArrayInputStream(geoJsonBytes);
            S3Utils.uploadFile(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getEndpoint(), RoverBucketEnum.ROVER_OPERATION.getName(), fileKey, inputStream);

            // 生成下载链接
            final Date expiration = DateUtil.offsetHour(new Date(), 24); // 链接有效期24小时
            URL downloadUrl = S3Utils.generatePresignUrl(s3Properties.getAccessKey(), s3Properties.getSecretKey(), s3Properties.getOutEndpoint(), RoverBucketEnum.ROVER_OPERATION.getName(), fileKey, HttpMethod.GET, expiration);

            // 返回结果
            return new MapCollectionTaskExportResultDTO(fileKey, downloadUrl.toString(), recordCount);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    /**
     * 查询勘查任务数据
     *
     * @param exportVO 导出参数
     * @return 勘查任务列表
     */
    private List<MapCollectionTask> queryMapCollectionTasks(MapCollectionTaskExportVO exportVO) {
        LambdaQueryWrapper<MapCollectionTask> queryWrapper = new LambdaQueryWrapper<>();

        // 设置任务状态条件
        if (CollUtil.isNotEmpty(exportVO.getTaskStatusList())) {
            queryWrapper.in(MapCollectionTask::getTaskStatus, exportVO.getTaskStatusList());
        } else {
            // 排除已删除的任务
            queryWrapper.ne(MapCollectionTask::getTaskStatus, TaskStatusEnum.TASK_DELETED.getCode());
        }

        // 设置城市ID条件
        if (CollUtil.isNotEmpty(exportVO.getCityId())) {
            queryWrapper.in(MapCollectionTask::getCityId, exportVO.getCityId());
        }

        // 设置站点ID条件
        if (CollUtil.isNotEmpty(exportVO.getStationId())) {
            queryWrapper.in(MapCollectionTask::getStationId, exportVO.getStationId());
        }

        // 设置任务ID列表条件
        if (CollUtil.isNotEmpty(exportVO.getTaskIdList())) {
            queryWrapper.in(BaseModel::getId, exportVO.getTaskIdList());
        }

        return mapCollectionTaskManager.getBaseMapper().selectList(queryWrapper);
    }


}