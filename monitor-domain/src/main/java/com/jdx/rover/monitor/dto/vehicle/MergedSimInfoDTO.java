/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 合并的SIM卡信息DTO
 * 将SimDataDTO和SimStatusDTO的字段根据deviceId合并在一起
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class MergedSimInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    // ========== SimDataDTO 字段 ==========
    
    /**
     * 制式
     * <p>例如：GSM, WCDMA, TD-SCDMA, LTE, 5G NR</p>
     */
    private String standard;

    /**
     * 频段
     * <p>例如：B1, B3, B5, B8, n41, n78等</p>
     */
    private String band;

    /**
     * 小区ID
     */
    private String communityId;

    /**
     * 基站ID
     */
    private String stationId;

    /**
     * RSRP (参考信号接收功率)
     * <p>单位：dBm，通常范围在-140到-40之间，值越大信号越强</p>
     */
    private Float rsrp;

    /**
     * SINR (信号与干扰加噪声比)
     * <p>单位：dB，通常范围在-20到30之间，值越大信号质量越好</p>
     */
    private Float sinr;

    /**
     * 日上传流量
     * <p>单位：MB</p>
     */
    private Double dailyUpload;

    /**
     * 日下载流量
     * <p>单位：MB</p>
     */
    private Double dailyDownload;

    /**
     * SIM数据记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date dataRecordTime;

    // ========== SimStatusDTO 字段 ==========

    /**
     * 在线状态
     * <p>0-未知，1-离线，2-在线</p>
     */
    private Integer onlineStatus;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date onlineTime;

    /**
     * 离线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date offlineTime;

    /**
     * 是否主卡
     */
    private Boolean mainCard;

    /**
     * 是否正在使用
     */
    private Boolean inUse;

    /**
     * SIM状态记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date statusRecordTime;
}
