/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SIM卡状态DTO
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class SimStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 在线状态
     * <p>0-未知，1-离线，2-在线</p>
     */
    private Integer onlineStatus;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date onlineTime;

    /**
     * 离线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date offlineTime;

    /**
     * 是否主卡
     */
    private Boolean mainCard;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 是否正在使用
     */
    private Boolean inUse;
}
